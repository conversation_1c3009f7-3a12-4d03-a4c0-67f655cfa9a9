# -*- coding: utf-8 -*-
"""1_car_price.ipynb

Automatically generated by Colab.

Original file is located at
    https://colab.research.google.com/drive/137uedrb6uNnTA_fDNn7WhrCH02IRYHpw

# Car price

## Imports
"""

import numpy as np
import pandas as pd

import matplotlib.pyplot as plt
import seaborn as sns

np.set_printoptions(suppress=True)
pd.set_option('display.float_format', lambda x: '%.3f' % x)
sns.set_theme()

"""## Read data"""

df = pd.read_csv("../data/car_price_dataset.csv")
df.shape

df.head()

"""## Analysis"""

df.describe()

df.isna().sum()

"""### Univariate"""

df["Brand"].value_counts()

df["Brand"].value_counts(normalize=True)

df["Year"].value_counts().sort_index().head()

"""#### Graphs"""

gp = df["Brand"].value_counts()
gp = gp.reset_index()

sns.barplot(data=gp, x="Brand", y="count")

plt.xticks(rotation=45);

plt.show()
# plt.savefig('ex.png', dpi=200, bbox_inches='tight')

gp = df["Fuel_Type"].value_counts()
gp = gp.reset_index()

sns.barplot(data=gp, x="Fuel_Type", y="count")

plt.xticks(rotation=45);

plt.show()
# plt.savefig('ex.png', dpi=200, bbox_inches='tight')

"""### Bivariate"""

df.groupby("Brand").agg({"Price": ["min", "mean", "median", "max"]})

"""#### Graphs"""

gp = df.groupby("Brand").agg(max=("Mileage", "max"))
gp.reset_index(inplace=True)
gp

sns.barplot(gp, x="Brand", y="max")

plt.ylim(295000, 302000)

plt.xticks(rotation=45);

plt.show()
# plt.savefig('ex.png', dpi=200, bbox_inches='tight')

sns.boxplot(df, x='Brand', y='Price')

plt.xticks(rotation=45);

plt.show()
# plt.savefig('ex.png', dpi=200, bbox_inches='tight')

gp = df.groupby(["Year", "Brand"]).agg(mean=("Price", "mean"))
gp.reset_index(inplace=True)
gp

sns.lineplot(gp, x="Year", y="mean", hue="Brand", marker="o")

gp_asia = gp[gp["Brand"].isin(["Honda", "Hyundai", "Kia", "Toyota"])]

sns.lineplot(gp_asia, x="Year", y="mean", hue="Brand", marker="o")

plt.show()
# plt.savefig('ex.png', dpi=200, bbox_inches='tight')

gp = df.groupby(["Year", "Brand"]).agg(mean=("Price", "mean")) / df.groupby(["Year"]).agg(mean=("Price", "mean"))
gp.reset_index(inplace=True)
gp

gp_asia = gp[gp["Brand"].isin(["Honda", "Hyundai", "Kia", "Toyota"])]

sns.lineplot(gp_asia, x="Year", y="mean", hue="Brand", marker="o")

gp = df.groupby(["Year", "Fuel_Type"]).agg(count=("Fuel_Type", "count"))
gp.reset_index()

sns.lineplot(gp, x="Year", y="count", hue="Fuel_Type", marker="o")

df.head()

cat_cols = df.select_dtypes(exclude='number').columns

for col in cat_cols:
    gp = df.groupby(["Year", col])["Price"].mean()
    gp = gp.reset_index()

    plt.clf()
    sns.lineplot(gp, x="Year", y="Price", hue=col, marker='o')
    plt.title(col)

    plt.show()
    # plt.savefig(f'ex_{col}.png', dpi=200, bbox_inches='tight')

num_cols = df.select_dtypes(include='number').columns

corr = df[num_cols].corr()
sns.heatmap(corr,
            xticklabels=corr.columns.values,
            yticklabels=corr.columns.values,
            annot=True)

plt.show()
# plt.savefig('ex.png', dpi=200, bbox_inches='tight')

"""## Feature engineering and selection"""

df.head()

"""### Mix"""

df["Brand_Model"] = df["Brand"] + "_" + df["Model"]

df["Brand_Model"].head()

"""### Binning"""

df["Year_cat"], bins = pd.qcut(df["Year"], 4, labels=False, retbins=True)
bins

df["Mileage_cat"], bins = pd.qcut(df["Mileage"], 10, labels=False, retbins=True)
bins

"""### Select"""

df.head()

sel_cols = ["Year",
            "Engine_Size",
            "Mileage",
            "Fuel_Type",
            "Transmission",
            "Price" # target
]

df = df[sel_cols]

"""## Modelling"""

x = df.copy()
y = x.pop("Price")

"""### Transform"""

from sklearn.preprocessing import OrdinalEncoder

x.head()

enc = OrdinalEncoder()
enc.fit_transform([x["Fuel_Type"]])

# Fuel Type
x.loc[x["Fuel_Type"] == "Petrol", "Fuel_Type"] = 1
x.loc[x["Fuel_Type"] == "Diesel", "Fuel_Type"] = 2
x.loc[x["Fuel_Type"] == "Hybrid", "Fuel_Type"] = 3
x.loc[x["Fuel_Type"] == "Electric", "Fuel_Type"] = 4

# Transmission
x.loc[x["Transmission"] == "Manual", "Transmission"] = 1
x.loc[x["Transmission"] == "Semi-Automatic", "Transmission"] = 2
x.loc[x["Transmission"] == "Automatic", "Transmission"] = 3

x["Fuel_Type"] = x["Fuel_Type"].astype(int)
x["Transmission"] = x["Transmission"].astype(int)

x.head()

"""### Split"""

from sklearn.model_selection import train_test_split

x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.33, random_state=42)

"""### Train"""

from sklearn.tree import DecisionTreeRegressor

reg = DecisionTreeRegressor(random_state=42)
reg.fit(x_train, y_train)

imp = reg.feature_importances_
cols = x_train.columns

pd.DataFrame({"column": cols, "importance": imp}).style.background_gradient()

from sklearn.tree import plot_tree

plot_tree(reg, filled=True, proportion=True)
plt.show()

"""## Performance evaluation"""

from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

y_pred = reg.predict(x_test)

mae = mean_absolute_error(y_test, y_pred)
mse = mean_squared_error(y_test, y_pred)
r_squared = r2_score(y_test, y_pred)
rmse = np.sqrt(mse)

print(f"MAE: {mae}")
print(f"MSE: {mse}")
print(f"RSQUARED: {r_squared}")
print(f"RMSE: {rmse}")

import shap

explainer = shap.TreeExplainer(reg, x)
shap_values = explainer(x, check_additivity=False)
shap.summary_plot(shap_values,
                  x,
                  max_display=x.shape[1],
                  feature_names=list(x.columns),
                  # show=False)
                 )

# plt.savefig('ex.png', dpi=200, bbox_inches='tight')

shap.plots.waterfall(shap_values[0],
                     # show=False
                    )

# plt.savefig('ex.png', dpi=200, bbox_inches='tight')