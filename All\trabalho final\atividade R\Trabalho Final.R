# Carregar os dados
df <- read.csv(file.choose())

# Seleção de variáveis-alvo
previous_scores <- df$Previous_Scores
exam_score <- df$Exam_Score

# Medidas de Tendência Central
mean_previous <- mean(previous_scores)
mean_exam <- mean(exam_score)

median_previous <- median(previous_scores)
median_exam <- median(exam_score)

mode_calc <- function(x) {
  uniq_x <- unique(x)
  uniq_x[which.max(tabulate(match(x, uniq_x)))]
}
mode_previous <- mode_calc(previous_scores)
mode_exam <- mode_calc(exam_score)

# Medidas de Variabilidade
variance_previous <- var(previous_scores, na.rm = TRUE)
variance_exam <- var(exam_score, na.rm = TRUE)

sd_previous <- sd(previous_scores, na.rm = TRUE)
sd_exam <- sd(exam_score, na.rm = TRUE)

cv_previous <- (sd_previous / mean_previous) * 100
cv_exam <- (sd_exam / mean_exam) * 100

# Percentis e Quartis
summary_previous <- summary(previous_scores)
summary_exam <- summary(exam_score)

# Boxplot
boxplot(previous_scores, main = "Boxplot - Previous Scores", col = "blue")
boxplot(exam_score, main = "Boxplot - Exam Scores", col = "green")

# Teste de Normalidade - Anderson-Darling
if (!require("nortest")) install.packages("nortest", dependencies = TRUE)
library(nortest)

ad_previous <- ad.test(previous_scores)
ad_exam <- ad.test(exam_score)

# Correlação
correlation_pearson <- cor(previous_scores, exam_score, method = "pearson")
correlation_spearman <- cor(previous_scores, exam_score, method = "spearman")

# Regressão Linear
lm_model <- lm(exam_score ~ previous_scores)
summary_lm <- summary(lm_model)

# Gráfico de Regressão Linear
plot(previous_scores, exam_score, main = "Regression Analysis",
     xlab = "Previous Scores", ylab = "Exam Score", col = "blue", pch = 19)
abline(lm_model, col = "red", lwd = 2)

# Resultados
cat("Medidas de Tendência Central\n")
cat("Média Previous Scores:", mean_previous, "\n")
cat("Média Exam Scores:", mean_exam, "\n")
cat("Mediana Previous Scores:", median_previous, "\n")
cat("Mediana Exam Scores:", median_exam, "\n")
cat("Moda Previous Scores:", mode_previous, "\n")
cat("Moda Exam Scores:", mode_exam, "\n\n")

cat("Medidas de Variabilidade\n")
cat("Variância Previous Scores:", variance_previous, "\n")
cat("Variância Exam Scores:", variance_exam, "\n")
cat("Desvio-padrão Previous Scores:", sd_previous, "\n")
cat("Desvio-padrão Exam Scores:", sd_exam, "\n")
cat("Coeficiente de Variação Previous Scores:", cv_previous, "%\n")
cat("Coeficiente de Variação Exam Scores:", cv_exam, "%\n\n")

cat("Teste de Normalidade - Anderson-Darling\n")
print(ad_previous)
print(ad_exam)

cat("\nCorrelação\n")
cat("Correlação Pearson:", correlation_pearson, "\n")
cat("Correlação Spearman:", correlation_spearman, "\n\n")

cat("Regressão Linear\n")
print(summary_lm)
