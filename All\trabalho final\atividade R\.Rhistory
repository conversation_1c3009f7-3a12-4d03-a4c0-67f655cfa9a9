# Nome: <PERSON>
# Tema/Dataset: Student Performance Factors
# Carregar o arquivo CSV
df_csv <- read.csv(file.choose())
df_csv
# Nome das colunas
#colnames(df_csv)
# Dimensão da tabela
dim(df_csv)
# Medidas de tendência central
# Média
mean(df_csv$Previous_Scores)
mean(df_csv$Exam_Score)
# Mediana
median(df_csv$Previous_Scores)
median(df_csv$Exam_Score)
# Moda
desem_na <- df_csv$Previous_Scores
desem_nf <- df_csv$Exam_Score
f.desem_na <- table(desem_na)
f.desem_nf <- table(desem_nf)
fmax_na <- max(f.desem_na)
fmax_nf <- max(f.desem_nf)
# Encontrar o valor correspondente da frequência máxima
moda1 <- (names(f.desem_na[f.desem_na == fmax_na]))
moda1
moda2 <- (names(f.desem_nf[f.desem_nf == fmax_nf]))
moda2
# Medidas de variabilidade
# Variância
var(df_csv$Previous_Scores)
var(df_csv$Exam_Score)
# Desvio-padrão
dp1 <- sd(df_csv$Previous_Scores)
dp1
dp2 <- sd(df_csv$Exam_Score)
dp2
# Coeficiente de variação
coef_var1 <- dp1 / mean(df_csv$Previous_Scores) * 100
coef_var1
coef_var2 <- dp2 / mean(df_csv$Exam_Score) * 100
coef_var2
# Medidas de posição (percentis e quartis)
summary(df_csv$Previous_Scores)
summary(df_csv$Exam_Score)
# Percentis Exames Anteriores
percentil1 <- quantile(df_csv$Previous_Scores, probs = c(0.01, 0.25, 0.50, 0.75, 1))
percentil1
# Percentis Exame Final
percentil2 <- quantile(df_csv$Exam_Score, probs = c(0.01, 0.25, 0.50, 0.75, 1))
percentil2
# Boxplot
boxplot(df_csv$Previous_Scores, main = "Boxplot - Previous Scores", col = "blue")
boxplot(df_csv$Exam_Score, main = "Boxplot - Exam Scores", col = "green")
# Teste de normalidade - Anderson-Darling
if (!require("nortest")) install.packages("nortest", dependencies = TRUE)
library(nortest)
ad_previous <- ad.test(df_csv$Previous_Scores)
ad_previous
ad_exam <- ad.test(df_csv$Exam_Score)
ad_exam
# Correlação
cor_pearson <- cor(df_csv$Previous_Scores, df_csv$Exam_Score, method = "pearson")
cor_pearson
cor_spearman <- cor(df_csv$Previous_Scores, df_csv$Exam_Score, method = "spearman")
cor_spearman
# Regressão Linear
m1 <- lm(df_csv$Exam_Score ~ df_csv$Previous_Scores)
summary(m1)
# Gráfico de regressão linear
plot(df_csv$Previous_Scores, df_csv$Exam_Score,
main = "Linear Regression - Previous Scores vs Exam Score",
xlab = "Previous Scores", ylab = "Exam Score", col = "blue", pch = 19)
abline(m1, col = "red", lwd = 2)
# Regressão Logística
df_csv$Success <- ifelse(df_csv$Exam_Score >= 70, 1, 0)
modelo_log <- glm(Success ~ Previous_Scores, data = df_csv, family = binomial)
summary(modelo_log)
# Gráfico de regressão logística
plot(df_csv$Previous_Scores, df_csv$Success,
main = "Logistic Regression - Previous Scores vs Success",
xlab = "Previous Scores", ylab = "Success (0 or 1)", col = "blue", pch = 19)
curve(predict(modelo_log, data.frame(Previous_Scores = x), type = "response"),
add = TRUE, col = "red", lwd = 2)
