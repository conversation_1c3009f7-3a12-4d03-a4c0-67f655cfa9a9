# Criando tabelas de frequência
df_csv = read.csv(file.choose())
View(df_csv)

# Frequência da Variável Tamanho da Empresa
# Frequência Absoluta
fa<-table(df_csv $ company_size)
fa

# Frequência Relativa
fr<-100*prop.table(fa)
fr

# Arredondamento Frequência
fr<-round(fr, digits = 2)
fr

# Adicionando <PERSON> de Total para Frequências (Frequência Acumulada)
fa<-c(fa, sum(fa))
fr<-c(fr, sum(fr))

tabela<-cbind(fa, fr)
tabela

# Tabela Contingência
cont_fa<-table(df_csv $ experience_level, df_csv $ employment_type)
cont_fa

cont_fr<-100*prop.table(cont_fa)
cont_fr

# Gráfico de Barras
# GB Simples
barplot(fr, beside = T)

# GB Contingência
barplot(cont_fr, beside = T)

# Gráfico de Setores/Torta
# Frequência Relativa
fa2<-table(df_csv $ company_size)
fr2<-100*prop.table(fa2)
pie(fr2)

# Gráfico de Dispersão
youtube = read.csv(file.choose())
View(youtube)

plot(youtube $ YouTube.Ads.Revenue..USD.,
     main = "Scatter Plot of YouTube Ads Revenue",
     xlab = "Index", 
     ylab = "YouTube Ads Revenue (USD)", 
     col = "blue", 
     pch = 19)

# Gráfico BOXPLOT
boxplot(youtube $ YouTube.Ads.Revenue..USD.)
#boxplot(df_csv $ salary_in_usd)

# Gráfico BOXPLOT com mais de uma variável
boxplot(youtube $ YouTube.Ads.Revenue..USD. ~ youtube $ Day.of.Week)

# Teste de Normalidade
# Histograma
# gerar números aleatório
set.seed(123)

# gerar 1000 dados normalmente distribuidos
dados<-rnorm(1000)
hist(dados, main = "Histograma de Dados", xlab = "Valor", ylab = "Frequência")

# histograma com dados do Youtube
hist(youtube $ Likes, main = "Histograma de Likes", xlab = "Número de Likes", ylab = "Frequência")

# Teste Shapiro-Wilk
# para dados
shapiro.test(dados)

# para likes do Youtube
options(scipen = 9999)
shapiro.test(youtube $ Likes)

# Correlação de Pearson (paramétrica)
# gerar números aleatórios
set.seed(123)

# número de observações
n<-100

# gerando dados normalmente distribuidos para X
a<-rnorm(n, mean = 50, sd = 10)

# gerando dados normalmente distribuidos para Y
b<-2*a+rnorm(n, mean = 0, sd = 5)

# visualizando os dados
plot(a, b, main = "dispersão dos dados", xlab = "x", ylab = "y")

cor_pearson<-cor(a, b, method = "pearson")
cor_pearson

cor.test(a, b, method = "pearson")

# Correlação de Spearman (não-paramétrica)
plot(youtube $ Views, youtube $ Shares, main = "Dispersão dos Dados", xlab = "Visualizações", ylab = "Compartilhamentos")
cor_spearman<-cor(youtube  $ Views, youtube $ Shares, method = "spearman")
cor_spearman

# Regressão Linear
# inspecionar a linearidade
plot(youtube $ YouTube.Ads.Revenue..USD., youtube $ Views)

m1<-lm(youtube $ YouTube.Ads.Revenue..USD. ~ youtube $ Views)
hist(residuals(m1))

# Teste de Normalidade
shapiro.test(residuals(m1))

# Regressão Linear (Dados Hipotéticos)
set.seed(123)
x<-rnorm(100, mean = 50, sd = 10)
y<-2 * x + rnorm(100, mean = 0, sd = 5)

# ajuste do modelo linear
m2<-lm(y~x)

# x é a minha variável independente (indepente) ou explicativa
# y é a minha variável dependente (depente) ou desfecho

plot(x, y)

# adicionando a reta de mínimos quadrados
abline(m2, col = "red", lwd = 2)

# equação da reta
coeficientes<-coef(m2)

cat ("Equação da Reta: Y = ", round(coeficientes[1], 2), " + ", round(coeficientes[2], 2), " * X\n")

summary(m2)

# Regressão Logística
set.seed(123)

yt<-data.frame(views_yt = rnorm(100, mean = 500, sd = 50), Success_yt = rbinom(100, 1, prob = 0.5))

# Passo 1: Ajustar o modelo para regressão logística
modelo_log<-glm(Success_yt ~ views_yt, data = yt, family = binomial)

# resultado da regressão logística
summary(yt)


















