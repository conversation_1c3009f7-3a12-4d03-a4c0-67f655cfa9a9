# Exercício 1 - E<PERSON>lher um conjunto de dados que tenha pelo menos 1 métrica quantitativa
# (1) Aplicar os cálculos vistos em aula
# (2) Gerar um texto de descrição e interpretação com base nos cálculos

# Tema: Student Performance Factors
# (1) Aplicar os cálculos vistos em aula:

file.choose()
df_csv = read.csv(file.choose())
df_csv

#nome das colunas
colnames(df_csv)

#dimensão da tabela
dim(df_csv)

#medidas de tendência central
#média
mean(df_csv $ Exam_Score)
mean(df_csv $ Previous_Scores)

#mediana
median(df_csv $ Exam_Score)
median(df_csv $ Previous_Scores)

#moda
desem_nf<-df_csv $ Exam_Score
desem_nf

desem_na<-df_csv $ Previous_Scores
desem_na

f.desem_nf<-table(desem_nf)
f.desem_na<-table(desem_na)

fmax_nf<-max(f.desem_nf)
fmax_nf

fmax_na<-max(f.desem_na)
fmax_na

#encontrar o valor correspondente da frequência máxima
moda1<-(names(f.desem_nf[f.desem_nf == fmax_nf]))
moda1

moda2<-(names(f.desem_na[f.desem_na == fmax_na]))
moda2

#medidas de variabilidade
#variância
var(df_csv $ Exam_Score)
var(df_csv $ Previous_Scores)

#desvio-padrão
dp1<-sd(df_csv $ Exam_Score)
dp1

dp2<-sd(df_csv $ Previous_Scores)
dp2

#coeficiente de variação
coef_var1<-dp/mean(df_csv $ Exam_Score)
coef_var1

coef_var2<-dp/mean(df_csv $ Previous_Scores)
coef_var2

#medidas de posição
#sumarização de variáveis
summary(df_csv $ Exam_Score)
summary(df_csv $ Previous_Scores)

#percentil Exame Final
percentil1<-quantile((df_csv $ Exam_Score), probs = 0.01)
percentil1

#percentil Exames Anteriores
percentil2<-quantile((df_csv $ Previous_Scores), probs = 0.01)
percentil2

# (2) Gerar um texto de descrição e interpretação com base nos cálculos
# Sobre cada valor:
Os valores de Média(s) indicam que o desempenho, entre os valores dos exames anteriores (75,07) para a nota final (67,24), diminuiram um pouco (7.83); indicando que a performance dos alunos diminuiu.

# Sucintamente:
De forma geral, a análise dos cálculos apontam que as notas do exame final são mais "aglomeradas" (próximas), 
apresentando menor variabilidade em torno da média; isso sugere que o desempenho da turma, de modo geral, não apresenta muita variação, 
apesar que não serem "melhores" que as notas anteriores. E por mais que a menor e a maior nota dos exames finais seja maior que o mínimo e máximo dos exames anteriores,
os quartis indicam que nos exames finais 75% dos alunos dos alunos pontuaram um nota menor que 69, enquanto no exames anteriores o valor é 88; isso indica que mais alunos
tiveram um desempenho melhor nos exames anteriores.
Entretanto, pelos os percentis indicam que o houve um leve melhora em comparação com os exames anteriores, então as menores notas são um pouco maior que dos exames anteriores.

Já as pontuações dos exames anteriores têm uma variação considerável, sugerindo, por meio dos outros fatores incluidos no dataset 
(como horas de sono, atividades extras, etc.), que o decréscimo do desempenho com os exames finais, foi causo por fatiga e exaustão dos alunos de modo geral.
