{"cells": [{"cell_type": "code", "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import re\n", "from tabulate import tabulate\n", "\n", "material_palette = [\"#1E88E5\", \"#D81B60\", \"#FFC107\", \"#43A047\", \"#8E24AA\", \"#F4511E\"]\n", "sns.set_palette(material_palette)"], "metadata": {"id": "2Xa8M7WDfhy2"}, "id": "2Xa8M7WDfhy2", "execution_count": 1, "outputs": []}, {"cell_type": "markdown", "source": ["### Pré-processamento"], "metadata": {"id": "AtZuerg6C_XP"}, "id": "AtZuerg6C_XP"}, {"cell_type": "code", "source": ["df = pd.read_csv('/content/produtos_ar_condicionado_20250319_124533.csv')"], "metadata": {"id": "uSbE3TBrgfnV"}, "id": "uSbE3TBrgfnV", "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 293}, "id": "LzaNYxrchc4i", "outputId": "8164e90a-668b-46bf-8b10-4ca1b208d4fd"}, "id": "LzaNYxrchc4i", "execution_count": 3, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                        nome_produto        preco  \\\n", "0  Ar-Condicionado Split Hi Wall Gree G-Top Auto ...  R$ 2.429,02   \n", "1  Ar-Condicionado Split Hi Wall LG Dual Inverter...  R$ 2.753,15   \n", "2  Ar-Condicionado Split Hi Wall Elgin Eco II 120...  R$ 2.279,05   \n", "3  Ar-Condicionado Split Hi Wall Samsung Wind Fre...  R$ 2.999,00   \n", "4  Ar-Condicionado Split Hi Wall Springer Midea A...  R$ 2.359,80   \n", "\n", "                    loja_vendedor  a<PERSON>  \\\n", "0                             NaN        NaN   \n", "1       Menor preço via FRIOPEÇAS   4.3 (57)   \n", "2  Menor preço via Webcontinental  4.5 (110)   \n", "3     Menor preço via Casas Bahia  4.6 (200)   \n", "4      Menor preço via Americanas   4.2 (79)   \n", "\n", "                                                 url  \n", "0  https://www.buscape.com.brhttps://www.buscape....  \n", "1  https://www.buscape.com.br/ar-condicionado/ar-...  \n", "2  https://www.buscape.com.br/ar-condicionado/ar-...  \n", "3  https://www.buscape.com.br/ar-condicionado/ar-...  \n", "4  https://www.buscape.com.br/ar-condicionado/ar-...  "], "text/html": ["\n", "  <div id=\"df-86faadb9-7160-4897-a2b7-86d322025e04\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nome_produto</th>\n", "      <th>preco</th>\n", "      <th>loja_vendedor</th>\n", "      <th>avaliacao</th>\n", "      <th>url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Ar-Condicionado Split Hi Wall Gree G-Top Auto ...</td>\n", "      <td>R$ 2.429,02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>https://www.buscape.com.brhttps://www.buscape....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Ar-Condicionado Split Hi Wall LG Dual Inverter...</td>\n", "      <td>R$ 2.753,15</td>\n", "      <td><PERSON><PERSON> pre<PERSON>o via FRIOPEÇAS</td>\n", "      <td>4.3 (57)</td>\n", "      <td>https://www.buscape.com.br/ar-condicionado/ar-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Ar-Condicionado Split Hi Wall Elgin Eco II 120...</td>\n", "      <td>R$ 2.279,05</td>\n", "      <td><PERSON><PERSON> pre<PERSON> via Webcontinental</td>\n", "      <td>4.5 (110)</td>\n", "      <td>https://www.buscape.com.br/ar-condicionado/ar-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ar-Condicionado Split Hi Wall Samsung Wind Fre...</td>\n", "      <td>R$ 2.999,00</td>\n", "      <td><PERSON><PERSON> pre<PERSON> via Casas Bahia</td>\n", "      <td>4.6 (200)</td>\n", "      <td>https://www.buscape.com.br/ar-condicionado/ar-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ar-Condicionado Split Hi Wall Springer Midea A...</td>\n", "      <td>R$ 2.359,80</td>\n", "      <td><PERSON><PERSON> via Americanas</td>\n", "      <td>4.2 (79)</td>\n", "      <td>https://www.buscape.com.br/ar-condicionado/ar-...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-86faadb9-7160-4897-a2b7-86d322025e04')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-86faadb9-7160-4897-a2b7-86d322025e04 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-86faadb9-7160-4897-a2b7-86d322025e04');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-85fe7efe-22bd-42ee-901f-303b33aedbfd\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-85fe7efe-22bd-42ee-901f-303b33aedbfd')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-85fe7efe-22bd-42ee-901f-303b33aedbfd button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 99,\n  \"fields\": [\n    {\n      \"column\": \"nome_produto\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 98,\n        \"samples\": [\n          \"Ar-Condicionado Split Hi Wall Springer Midea AirVolution 12000 BTUs Frio 42AFFCG12S5 / 38TFCB12S5\",\n          \"Ar-Condicionado Split Hi Wall Philco Eco 12000 BTUs Frio Inverter PAC12000ITFM9W\",\n          \"Ar-Condicionado Split Hi Wall LG Dual Inverter Compact 12000 BTUs Controle Remoto Quente/Frio +AI S3-W12JAQAL\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"preco\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 95,\n        \"samples\": [\n          \"R$ 2.759,08\",\n          \"R$ 3.357,21\",\n          \"R$ 2.959,00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"loja_vendedor\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 15,\n        \"samples\": [\n          \"<PERSON>or pre\\u00e7o via Leveros\",\n          \"<PERSON>or pre\\u00e7o via Lojas MM\",\n          \"<PERSON>or pre\\u00e7o via FRIOPE\\u00c7AS\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avaliacao\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 45,\n        \"samples\": [\n          \"4.2 (136)\",\n          \"4.9 (27)\",\n          \"5 (5)\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"url\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 99,\n        \"samples\": [\n          \"https://www.buscape.com.br/ar-condicionado/ar-condicionado-split-hi-wall-agratto-12000-btus-frio-facst12f?_lc=11\",\n          \"https://www.buscape.com.br/ar-condicionado/ar-condicionado-split-hi-wall-samsung-wind-free-connect-12000-btus-quente-frio-inverter-ar12bseaawknaz-ar12bseaawkxaz?_lc=11\",\n          \"https://www.buscape.com.br/ar-condicionado/ar-condicionado-split-hi-wall-lg-dual-inverter-compact-12000-btus-quente-frio-inverter-ai-s3-w12jaqal?_lc=11\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 3}]}, {"cell_type": "code", "source": ["df.info()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Sml-G-RMf5P8", "outputId": "da585998-3829-4205-93a9-f741b81d1026"}, "id": "Sml-G-RMf5P8", "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 99 entries, 0 to 98\n", "Data columns (total 5 columns):\n", " #   Column         Non-Null Count  Dtype \n", "---  ------         --------------  ----- \n", " 0   nome_produto   99 non-null     object\n", " 1   preco          99 non-null     object\n", " 2   loja_vendedor  98 non-null     object\n", " 3   avaliacao      46 non-null     object\n", " 4   url            99 non-null     object\n", "dtypes: object(5)\n", "memory usage: 4.0+ KB\n"]}]}, {"cell_type": "code", "source": ["df.drop(columns=[\"url\"], inplace=True)"], "metadata": {"id": "Rg5ylZqZl0tX"}, "execution_count": 5, "outputs": [], "id": "Rg5ylZqZl0tX"}, {"cell_type": "code", "source": ["# limpa o campo 'preco'.\n", "def clean_price(price):\n", "    if isinstance(price, str):\n", "        cleaned = re.sub(r'[^\\d.,]', '', price)\n", "        cleaned = cleaned.replace('.', '')\n", "        cleaned = cleaned.replace(',', '.')\n", "        return float(cleaned)\n", "    return None\n", "\n", "df['preco'] = df['preco'].apply(clean_price)"], "metadata": {"id": "JGoxkM7Mf5AF"}, "id": "JGoxkM7Mf5AF", "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["# dividi o campo 'avaliacao' em duas colunas.\n", "def split_avaliacao(avaliacao):\n", "    if pd.notnull(avaliacao):\n", "        match = re.match(r\"([\\d.]+)\\s*\\((\\d+)\\)\", str(avaliacao))\n", "        if match:\n", "            return float(match.group(1)), int(match.group(2))\n", "    return None, None\n", "\n", "df[\"avaliacao_produto\"], df[\"numero_avaliadores\"] = zip(*df[\"avaliacao\"].apply(split_avaliacao))\n", "\n", "df.drop(columns=[\"avaliacao\"], inplace=True)"], "metadata": {"id": "ydJn4TW7jf39"}, "id": "ydJn4TW7jf39", "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["# limpa o campo 'loja_vendedor'.\n", "def clean_loja_vendedor(value):\n", "    if value and isinstance(value, str):\n", "        return value.replace(\"Menor preço via \", \"\").strip()\n", "    return value\n", "\n", "df[\"loja_vendedor\"] = df[\"loja_vendedor\"].apply(clean_loja_vendedor)"], "metadata": {"id": "gvlmjroNlaWf"}, "id": "gvlmjroNlaWf", "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "mHEUA91FjkXY", "outputId": "13129f4f-78dd-4570-b99a-3252c6ded691"}, "id": "mHEUA91FjkXY", "execution_count": 9, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                        nome_produto    preco   loja_vendedor  \\\n", "0  Ar-Condicionado Split Hi Wall Gree G-Top Auto ...  2429.02             NaN   \n", "1  Ar-Condicionado Split Hi Wall LG Dual Inverter...  2753.15       FRIOPEÇAS   \n", "2  Ar-Condicionado Split Hi Wall Elgin Eco II 120...  2279.05  Webcontinental   \n", "3  Ar-Condicionado Split Hi Wall Samsung Wind Fre...  2999.00     Casas Bahia   \n", "4  Ar-Condicionado Split Hi Wall Springer Midea A...  2359.80      Americanas   \n", "\n", "   avaliacao_produto  numero_avaliadores  \n", "0                NaN                 NaN  \n", "1                4.3                57.0  \n", "2                4.5               110.0  \n", "3                4.6               200.0  \n", "4                4.2                79.0  "], "text/html": ["\n", "  <div id=\"df-ef5a7ea9-1f3b-4add-a1a6-a2d8ddfbc7ab\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nome_produto</th>\n", "      <th>preco</th>\n", "      <th>loja_vendedor</th>\n", "      <th>a<PERSON><PERSON>ao_produto</th>\n", "      <th>numero_avaliadores</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Ar-Condicionado Split Hi Wall Gree G-Top Auto ...</td>\n", "      <td>2429.02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Ar-Condicionado Split Hi Wall LG Dual Inverter...</td>\n", "      <td>2753.15</td>\n", "      <td>FRIOPEÇAS</td>\n", "      <td>4.3</td>\n", "      <td>57.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Ar-Condicionado Split Hi Wall Elgin Eco II 120...</td>\n", "      <td>2279.05</td>\n", "      <td>Webcontinental</td>\n", "      <td>4.5</td>\n", "      <td>110.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ar-Condicionado Split Hi Wall Samsung Wind Fre...</td>\n", "      <td>2999.00</td>\n", "      <td>Casas Bahia</td>\n", "      <td>4.6</td>\n", "      <td>200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ar-Condicionado Split Hi Wall Springer Midea A...</td>\n", "      <td>2359.80</td>\n", "      <td>Americanas</td>\n", "      <td>4.2</td>\n", "      <td>79.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ef5a7ea9-1f3b-4add-a1a6-a2d8ddfbc7ab')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-ef5a7ea9-1f3b-4add-a1a6-a2d8ddfbc7ab button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-ef5a7ea9-1f3b-4add-a1a6-a2d8ddfbc7ab');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-c93443ac-5363-403d-82ed-3e61f5dd07b6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-c93443ac-5363-403d-82ed-3e61f5dd07b6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-c93443ac-5363-403d-82ed-3e61f5dd07b6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 99,\n  \"fields\": [\n    {\n      \"column\": \"nome_produto\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 98,\n        \"samples\": [\n          \"Ar-Condicionado Split Hi Wall Springer Midea AirVolution 12000 BTUs Frio 42AFFCG12S5 / 38TFCB12S5\",\n          \"Ar-Condicionado Split Hi Wall Philco Eco 12000 BTUs Frio Inverter PAC12000ITFM9W\",\n          \"Ar-Condicionado Split Hi Wall LG Dual Inverter Compact 12000 BTUs Controle Remoto Quente/Frio +AI S3-W12JAQAL\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"preco\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 508.33987729530776,\n        \"min\": 1954.63,\n        \"max\": 5099.15,\n        \"num_unique_values\": 95,\n        \"samples\": [\n          2759.08,\n          3357.21,\n          2959.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"loja_vendedor\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 15,\n        \"samples\": [\n          \"Leveros\",\n          \"Lojas MM\",\n          \"FRIOPE\\u00c7AS\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"avaliacao_produto\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.4417138339593985,\n        \"min\": 3.1,\n        \"max\": 5.0,\n        \"num_unique_values\": 15,\n        \"samples\": [\n          3.7,\n          5.0,\n          4.3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"numero_avaliadores\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 101.62867192146976,\n        \"min\": 1.0,\n        \"max\": 452.0,\n        \"num_unique_values\": 39,\n        \"samples\": [\n          2.0,\n          1.0,\n          130.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["filename=\"produtos_ar_condicionado_limpos.csv\"\n", "df.to_csv(filename, index=False, encoding=\"utf-8\")"], "metadata": {"id": "GRtI_Y4Bogwv"}, "id": "GRtI_Y4Bogwv", "execution_count": 10, "outputs": []}, {"cell_type": "markdown", "source": ["###Análise e Exportação"], "metadata": {"id": "fD5mVh7epRda"}, "id": "fD5mVh7epRda"}, {"cell_type": "code", "source": ["average_price = df['preco'].mean()\n", "print(f\"Preço médio dos produtos: R$ {average_price:.2f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bCLqYylhoEEE", "outputId": "e56c916b-49d5-4ea2-a7c3-7d34214f9414"}, "id": "bCLqYylhoEEE", "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Preço médio dos produtos: R$ 2812.67\n"]}]}, {"cell_type": "code", "source": ["# Remover linhas com valores NaN nos campos relevantes.\n", "df_clean = df.dropna(subset=[\"preco\", \"loja_vendedor\", \"avaliacao_produto\"])"], "metadata": {"id": "mhydAkPTwHoq"}, "id": "mhydAkPTwHoq", "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["# Responder às perguntas\n", "# 1. Qual é o preço médio praticado pelos concorrentes?\n", "preco_medio = df_clean[\"preco\"].mean()\n", "print(f\"1. O preço médio praticado pelos concorrentes é: R$ {preco_medio:.2f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WV6uG2qowHjk", "outputId": "03469c15-f5bc-44f1-e305-9c5c508a024b"}, "id": "WV6uG2qowHjk", "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["1. O preço médio praticado pelos concorrentes é: R$ 2768.09\n"]}]}, {"cell_type": "code", "source": ["# 2. <PERSON><PERSON><PERSON> lo<PERSON>/vendedores oferecem os melhores preços?\n", "# Ordenar por preço e selecionar as 5 lojas com os menores preços\n", "melhores_precos = df_clean.sort_values(by=\"preco\").head(5)\n", "\n", "print(\"\\n2. Lojas/vendedores com os melhores preços:\")\n", "tabela_melhores_precos = tabulate(\n", "    melhores_precos[[\"loja_vendedor\", \"preco\", \"nome_produto\", \"avaliacao_produto\", \"numero_avaliadores\"]],\n", "    headers=\"keys\",\n", "    tablefmt=\"grid\"\n", ")\n", "print(tabela_melhores_precos)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Q_LwcvpBwHh9", "outputId": "7fd45a26-c5e0-4f42-ab0a-80390cbfa0f4"}, "id": "Q_LwcvpBwHh9", "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "2. Lojas/vendedores com os melhores preços:\n", "+----+-----------------+---------+--------------------------------------------------------------------------------------------------------------------+---------------------+----------------------+\n", "|    | loja_vendedor   |   preco | nome_produto                                                                                                       |   avaliacao_produto |   numero_avaliadores |\n", "+====+=================+=========+====================================================================================================================+=====================+======================+\n", "| 14 | Amazon          | 1999    | Ar-Condicionado Split Hi Wall EOS Master Comfort 12000 BTUs Controle Remoto Frio EASM12000FI / EASM12000FE         |                 4.6 |                   45 |\n", "+----+-----------------+---------+--------------------------------------------------------------------------------------------------------------------+---------------------+----------------------+\n", "|  8 | Extra           | 2219.24 | Ar-Condicionado Split Hi Wall Springer Midea 12000 BTUs Frio Inverter AirVolution Connect 42AFVCI12S5 / 38TVCI12S5 |                 4.2 |                  245 |\n", "+----+-----------------+---------+--------------------------------------------------------------------------------------------------------------------+---------------------+----------------------+\n", "| 26 | Americanas      | 2267.01 | Ar-Condicionado Split Hi Wall Philco Eco 12000 BTUs Frio Inverter PAC12000IFM15                                    |                 4   |                   17 |\n", "+----+-----------------+---------+--------------------------------------------------------------------------------------------------------------------+---------------------+----------------------+\n", "|  2 | Webcontinental  | 2279.05 | Ar-Condicionado Split Hi Wall Elgin Eco II 12000 BTUs Frio Inverter HJFI12C2WB / HJFE12C2CB                        |                 4.5 |                  110 |\n", "+----+-----------------+---------+--------------------------------------------------------------------------------------------------------------------+---------------------+----------------------+\n", "| 13 | Pont<PERSON>           | 2300.4  | Ar-Condicion<PERSON> / Pa<PERSON>e Consul 12000 BTUs Frio CCB12EB                                                     |                 3.1 |                    3 |\n", "+----+-----------------+---------+--------------------------------------------------------------------------------------------------------------------+---------------------+----------------------+\n"]}]}, {"cell_type": "code", "source": ["# 3. Existe correlação entre preço e avaliação dos produtos?\n", "# Calcular a correlação entre preço e avaliação do produto\n", "correlacao = df_clean[\"preco\"].corr(df_clean[\"avaliacao_produto\"])\n", "if abs(correlacao) > 0.7:\n", "    interpretacao = \"forte correlação\"\n", "elif abs(correlacao) > 0.3:\n", "    interpretacao = \"moderada correlação\"\n", "else:\n", "    interpretacao = \"fraca ou nenhuma correlação\"\n", "\n", "print(f\"\\n3. A correlação entre preço e avaliação dos produtos é {correlacao:.2f}, indicando {interpretacao}.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6hnPwqnfxc-_", "outputId": "bf02775f-a6ca-4181-a524-a5acc84e2977"}, "id": "6hnPwqnfxc-_", "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "3. A correlação entre preço e avaliação dos produtos é 0.34, indicando moderada correlação.\n"]}]}, {"cell_type": "code", "source": ["# Filtra apenas produtos com avaliação válida\n", "valid_ratings = df.dropna(subset=['avaliacao_produto'])\n", "\n", "sns.set_style(\"whitegrid\")\n", "\n", "plt.figure(figsize=(10, 5))\n", "sns.regplot(data=valid_ratings, x='preco', y='avaliacao_produto', scatter_kws={'alpha': 0.7})\n", "plt.title(\"Gráfico de Dispersão: Preço vs Avaliação\", fontsize=14)\n", "plt.xlabel(\"Preço (R$)\", fontsize=12)\n", "plt.ylabel(\"Avaliação\", fontsize=12)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 493}, "id": "D1_fM7A4oDzd", "outputId": "ba3976d6-1dbf-46b4-adec-1f69a4c347a7"}, "id": "D1_fM7A4oDzd", "execution_count": 16, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["plt.figure(figsize=(10, 5))\n", "sns.histplot(data=df, x='preco', bins=20, alpha=0.7)\n", "plt.title(\"Histograma de Preços\", fontsize=14)\n", "plt.xlabel(\"Preço (R$)\", fontsize=12)\n", "plt.ylabel(\"Frequência\", fontsize=12)\n", "plt.grid(True)\n", "\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 492}, "id": "0cts8Qlcs48S", "outputId": "ecdc5355-cda5-40f7-c72f-4df8f3a208d3"}, "id": "0cts8Qlcs48S", "execution_count": 17, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["plt.figure(figsize=(12, 6))\n", "sns.boxplot(data=df, x='loja_vendedor', y='preco', hue='loja_vendedor', legend=False)\n", "plt.title(\"Distribuição de Preços por Loja\", fontsize=14)\n", "plt.xlabel(\"Loja\", fontsize=12)\n", "plt.ylabel(\"Preço (R$)\", fontsize=12)\n", "plt.xticks(rotation=45, ha='right')\n", "plt.grid(axis='y')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 616}, "id": "ANGUnLL6s4sc", "outputId": "7d02f1e4-a7cb-4f66-cb7d-3a500e3f9435"}, "id": "ANGUnLL6s4sc", "execution_count": 18, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["plt.figure(figsize=(12, 6))\n", "sns.countplot(data=df, x='loja_vendedor', hue='loja_vendedor', legend=False)\n", "plt.title(\"Quantidade de Produtos por Loja\", fontsize=14)\n", "plt.xlabel(\"Loja\", fontsize=12)\n", "plt.ylabel(\"Quantidade de Produtos\", fontsize=12)\n", "plt.xticks(rotation=45, ha='right')\n", "plt.grid(axis='y')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 627}, "id": "Rhwoqd8W7YNC", "outputId": "44550891-0866-49b4-f673-e4373b8dd40a"}, "id": "Rhwoqd8W7YNC", "execution_count": 19, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# Calculando a matriz de correlação\n", "correlation_matrix = df[['preco', 'avaliacao_produto', 'numero_avaliadores']].corr()\n", "\n", "# Plotando o heatmap\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(correlation_matrix, annot=True, fmt='.2f', linewidths=0.5)\n", "plt.title(\"<PERSON><PERSON> de Correlação\", fontsize=14)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 546}, "id": "6wIcfB577X6v", "outputId": "5e2a9603-79f4-4ce2-859d-b89fc2875b80"}, "id": "6wIcfB577X6v", "execution_count": 20, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "14epLFPT9EJ8"}, "id": "14epLFPT9EJ8", "execution_count": 20, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}