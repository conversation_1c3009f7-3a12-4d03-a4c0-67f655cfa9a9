#leitura do .csv
#função file.choose
file.choose()
df_csv = read.csv(file.choose())
df_csv

#nome das colunas
colnames(df_csv)

#dimensão da tabela
dim(df_csv)

#medidas de tendência central
#média
mean(df_csv $ salary_in_usd)
mean(df_csv $ work_year)

#mediana
median(df_csv $ salary_in_usd)

#moda
sal_usd<-df_csv$salary_in_usd
sal_usd

fre.sal_usd<-table(sal_usd)

fmax<-max(fre.sal_usd)
fmax

#encontrar o valor correspondente da frequência máxima
moda<-(names(fre.sal_usd[fre.sal_usd == fmax]))
moda

#medidas de variabilidade
#variância
var(df_csv $ salary_in_usd)

#desvio-padrão
dp<-sd(df_csv $ salary_in_usd)
dp

#coeficiente de variação
coef_var<-dp/mean(df_csv $ salary_in_usd)
coef_var

#medidas de posição
#sumarização de variáveis
summary(df_csv $ salary_in_usd)

#percentil
percentil1<-quantile((df_csv $ salary_in_usd), probs = 0.01)
percentil1

# Exercício 1 - Escolher um conjunto de dados que tenha pelo menos 1 métrica quantitativa
# (1) Aplicar os cálculos vistos em aula
# (2) Gerar um texto de descrição e interpretação com base nos cálculos
