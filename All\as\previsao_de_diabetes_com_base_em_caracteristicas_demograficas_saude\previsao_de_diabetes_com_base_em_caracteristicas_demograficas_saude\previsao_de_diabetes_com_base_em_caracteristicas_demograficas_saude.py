# -*- coding: utf-8 -*-
"""previsao_de_diabetes_com_base_em_caracteristicas_demograficas_saude.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1HkA98LjBPRHI1vMh0LpDkyZi9lHYn-be
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
from sklearn.preprocessing import StandardScaler, OneHotEncoder

plt.style.use('ggplot')
plt.rcParams['figure.figsize'] = (10, 6)
sns.set(font_scale=1.2)

material_palette = ["#1E88E5", "#D81B60", "#FFC107", "#43A047", "#8E24AA", "#F4511E"]
sns.set_palette(material_palette)

df = pd.read_csv("./diabetes_dataset.csv")

pd.set_option('display.max_columns', None)
display(df.head(10))

print(f"Formato do dataset: {df.shape}")
display(df.info())

summary = df.describe(include='all').transpose()
summary['missing_values'] = df.isna().sum()
summary.head(16)

summary['missing_percentage'] = (summary['missing_values'] / df.shape[0]) * 100
summary['missing_percentage'] = summary['missing_percentage'].apply(lambda x: f'{x:.1f}%')

missing_values_sorted = summary.sort_values(by='missing_values', ascending=False)

print(missing_values_sorted[['missing_values', 'missing_percentage']].head(10))

num_cols = df.select_dtypes(include=["int64", "float64"]).columns
cat_cols = df.select_dtypes(include=["object"]).columns

print(num_cols)

print(cat_cols)

diabetes_counts = df['diabetes'].value_counts()
diabetes_percent = df['diabetes'].value_counts(normalize=True) * 100

result = pd.DataFrame({
    'count': diabetes_counts,
    'percentage': diabetes_percent.round(2).astype(str) + '%'
})

result

result = df.groupby(['gender', 'diabetes']).size().reset_index(name='count')
total_by_gender = df.groupby('gender').size()

result['percentage'] = result.apply(
    lambda x: 100 * x['count'] / total_by_gender[x['gender']], axis=1
)

result['percentage'] = result['percentage'].round(2).astype(str) + '%'
result

result = df.groupby(['hypertension', 'diabetes']).size().reset_index(name='count')
total_by_hypertension = df.groupby('hypertension').size()

result['percentage'] = result.apply(
    lambda x: 100 * x['count'] / total_by_hypertension[x['hypertension']], axis=1
)

result['percentage'] = result['percentage'].round(2).astype(str) + '%'
result

result = df.groupby(['smoking_history', 'diabetes']).size().reset_index(name='count')
total_by_smoking = df.groupby('smoking_history').size()

result['percentage'] = result.apply(
    lambda x: 100 * x['count'] / total_by_smoking[x['smoking_history']], axis=1
)

result['percentage'] = result['percentage'].round(2).astype(str) + '%'
result

df.groupby(['heart_disease', 'diabetes']).agg({'heart_disease': 'count'})

plt.figure()
sns.color_palette()
sns.histplot(data=df, x='age', hue='gender', multiple='stack', bins=15)
plt.title('Distribuição de Idade por Gênero')
plt.xlabel('Idade')
plt.ylabel('Contagem')

plt.figure()
sns.color_palette()
race_cols = [col for col in df.columns if col.startswith('race:')]
race_diabetes = []

for race_col in race_cols:
    race_name = race_col.split(':')[1]
    diabetic_count = df[df[race_col] == 1]['diabetes'].sum()
    total_count = (df[race_col] == 1).sum()
    if total_count > 0:
        diabetes_rate = diabetic_count / total_count * 100
    else:
        diabetes_rate = 0
    race_diabetes.append({'Race': race_name, 'Diabetes Rate (%)': diabetes_rate})

race_df = pd.DataFrame(race_diabetes)
sns.barplot(data=race_df, x='Race', y='Diabetes Rate (%)')
plt.title('Taxa de Diabetes por Raça')
plt.xticks(rotation=45)
plt.tight_layout()

plt.figure()
sns.color_palette()
sns.scatterplot(data=df, x='blood_glucose_level', y='hbA1c_level', hue='diabetes')
plt.title('Relação entre Nível de Glicose e HbA1c')
plt.xlabel('Nível de Glicose Sanguínea')
plt.ylabel('Nível de HbA1c')

plt.figure()
sns.color_palette()
sns.kdeplot(data=df, x='bmi', hue='diabetes', fill=True)
plt.title('Distribuição de IMC por Status de Diabetes')
plt.xlabel('Índice de Massa Corporal (IMC)')
plt.ylabel('Densidade')

plt.figure(figsize=(20, 10))
sns.color_palette()
numeric_cols = num_cols
corr_matrix = df[numeric_cols].corr()
sns.heatmap(corr_matrix, annot=True, vmin=-1, vmax=1, center=0)
plt.title('Matriz de Correlação entre Variáveis Numéricas')
plt.tight_layout()

plt.figure()
sns.color_palette()
smoking_diabetes = df.groupby('smoking_history')['diabetes'].mean() * 100
smoking_diabetes = smoking_diabetes.reset_index()
sns.barplot(data=smoking_diabetes, x='smoking_history', y='diabetes')
plt.title('Prevalência de Diabetes por Histórico de Fumo (%)')
plt.xlabel('Histórico de Fumantes')
plt.ylabel('Taxa de Diabetes (%)')

plt.figure()
sns.color_palette()
yearly_diabetes = df.groupby('year')['diabetes'].mean() * 100
yearly_diabetes = yearly_diabetes.reset_index()
sns.lineplot(data=yearly_diabetes, x='year', y='diabetes', marker='o')
plt.title('Tendência de Diabetes ao Longo dos Anos')
plt.xlabel('Ano')
plt.ylabel('Taxa de Diabetes (%)')
plt.xticks(rotation=45)
plt.tight_layout()

plt.figure(figsize=(12, 6))
sns.color_palette()
disease_diabetes = pd.melt(df,
                          id_vars=['diabetes'],
                          value_vars=['hypertension', 'heart_disease'],
                          var_name='Condição',
                          value_name='Presente')

disease_summary = disease_diabetes.groupby(['diabetes', 'Condição'])['Presente'].mean().reset_index()
disease_summary['Presente'] = disease_summary['Presente'] * 100

sns.barplot(data=disease_summary, x='Condição', y='Presente', hue='diabetes')
plt.title('Prevalência de Hipertensão e Doença Cardíaca por Status de Diabetes')
plt.xlabel('Condição')
plt.ylabel('Prevalência (%)')
plt.legend(title='Diabetes', labels=['Não', 'Sim'])

df.drop(columns=["year", "location"], inplace=True)

y = df['diabetes']
X = df.drop(columns=['diabetes'])

num_cols = X.select_dtypes(include=["int64", "float64"]).columns
cat_cols = X.select_dtypes(include=["object"]).columns

num_transformer = Pipeline([
    ('scaler', StandardScaler())
])

cat_transformer = Pipeline([
    ('imputer', SimpleImputer(strategy='most_frequent')),
    ('encoder', OneHotEncoder(handle_unknown='ignore'))
])

preprocessor = ColumnTransformer([
    ('num', num_transformer, num_cols),
    ('cat', cat_transformer, cat_cols)
])

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42, stratify=y)

models = {
    'RandomForest': RandomForestClassifier(random_state=42),
    'LogisticRegression': LogisticRegression(max_iter=1000, random_state=42),
    'XGBoost': XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)
}

results = {}
for name, model in models.items():
    print(f"\n{'='*50}")
    print(f"Treinando modelo: {name}")
    print(f"{'='*50}")

    pipeline = Pipeline([
        ('preprocessor', preprocessor),
        ('classifier', model)
    ])

    print("Realizando validação cruzada...")
    scores = cross_val_score(pipeline, X_train, y_train, cv=5, scoring='accuracy')
    print(f"Acurácia CV (5-fold): {scores.mean():.4f} (±{scores.std():.4f})")

    print("Treinando modelo no conjunto completo de treino...")
    pipeline.fit(X_train, y_train)

    y_pred = pipeline.predict(X_test)

    results[name] = {
        'Accuracy': accuracy_score(y_test, y_pred),
        'Precision': precision_score(y_test, y_pred),
        'Recall': recall_score(y_test, y_pred),
        'F1': f1_score(y_test, y_pred),
        'CV Mean Accuracy': scores.mean(),
        'CV Std Accuracy': scores.std()
    }

    print(f"\nMétricas para {name}:")
    for metric, value in results[name].items():
        print(f"{metric}: {value:.4f}")

    print("\nClassification Report:")
    print(classification_report(y_test, y_pred))

    cm = confusion_matrix(y_test, y_pred)
    plt.figure(figsize=(8, 6))
    sns.color_palette()
    sns.heatmap(cm, annot=True, fmt='d', cbar=False)
    plt.title(f"Matriz de Confusão - {name}")
    plt.xlabel("Predito")
    plt.ylabel("Real")
    plt.xticks([0.5, 1.5], ['Não', 'Sim'])
    plt.yticks([0.5, 1.5], ['Não', 'Sim'])
    plt.show()

results_df = pd.DataFrame(results).T
display(results_df.sort_values(by='F1', ascending=False))

plt.figure(figsize=(14, 8))
sns.color_palette()
metrics = ['Accuracy', 'Precision', 'Recall', 'F1']
results_plot = results_df[metrics]
results_plot.plot(kind='bar', figsize=(12, 6))
plt.title('Comparação de Métricas entre Modelos')
plt.ylabel('Valor')
plt.xlabel('Modelo')
plt.ylim([0, 1])
plt.xticks(rotation=0)
plt.legend(loc='lower right')
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()
plt.show()

best_model_name = results_df.sort_values(by='Accuracy', ascending=False).index[0]
print(f"Melhor modelo baseado na Accuracy: {best_model_name}")

best_model_name = results_df.sort_values(by='Precision', ascending=False).index[0]
print(f"Melhor modelo baseado na Precision: {best_model_name}")

best_model_name = results_df.sort_values(by='F1', ascending=False).index[0]
print(f"Melhor modelo baseado na Precision: {best_model_name}")

best_model_name = results_df.sort_values(by='Recall', ascending=False).index[0]
print(f"Melhor modelo baseado na Precision: {best_model_name}")

