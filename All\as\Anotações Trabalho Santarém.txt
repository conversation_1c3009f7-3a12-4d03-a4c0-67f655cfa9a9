- a cada dia são publicadas novas pesquisas sobre llms, agentes;
eu penso que seria bom selecionar um corpus documental com artigos relacionados as tecnologias aplicadas a saúde e realizar uma revisão sistemática da literatura mapeando as principais tendencias das pesquisas, para entender e publicar o direcionamento das pesquisas sobre essas tecnologias

* (aula de estudos métricos, penso em realizar essa pesquisa)

- a ideia aqui é pensando em uma parceria com o pessoal de fisio, então a ideia é explicar como são recuperadas as respostas e no âmbito da validação é gerar alguma resposta assim e fazer uma curadoria humana com o pessoal da saúde, para ver se as respostas estão sendo satisfatórias. quando eu me refiro a mecanismos de transparecia e explicabilidade me refiro as respostas geradas pelo sistema prototipo que construiremos, a ideia é criar uma documentação explicando todos os procedimentos, por exemplo trazendo os trechos dos documentos recuperados pelo sistema e usados pelo proprio sistema para gerar a resposta, e sobre a validação, penso em usar frameworks como ragas para avaliar esse tipo de aplicação, mas tambem se possivel fazer uma curadoria humana com um conjuntos de perguntas e repostas gerados pelo sistema para que os profissionais avaliem e comparar as respostas
e dar o feedback se estão de acordo com o que estavam procurando sobre um paciente

*rag ele recuperar e rescreve

- então, sobre isso eu estava pensado sobre em pegar dados de pacientes para uma parceria (Se der certo como o professor sugeriu) como por exemplo da fisio, mas entendo que há questões importantes de privacidade, então penso que talvez tenhamos que criar um protocolo para uso responsável desses dados.

*as questões técnicas, de ajuste fino e rag, eu estou aprofundando mais os meus conhecimentos sobre essas tecnologias, eu tenho feitos alguns cursos e como do hugging face sobre agents que é um curso novo e recentes


- professor quando eu digo que potencializam o uso de dados clinicos, eu me refiro as possibilidades que esses sistemas trazem para recuperação da informação, que conforme eu entendi com a analise do corpus documental que eu selecionei para o pré-projeto, devido a quantidade esmagadora de dados para serem recuperados e também pelo fato de esses dados naturalmente trazerem  erros comuns de digitação, sinonimos, siglas, entre outros; então para um profissional é dificil recuperar dados realmente relevantes, por isso o uso dessas tecnologias tendem a recuperar a informação e o conhecimento contido nesses dados.

- 