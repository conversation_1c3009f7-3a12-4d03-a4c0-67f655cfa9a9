# Exercício 1 - E<PERSON>lher um conjunto de dados que tenha pelo menos 1 métrica quantitativa
# (1) Aplicar os cálculos vistos em aula
# (2) Gerar um texto de descrição e interpretação com base nos cálculos

# Nome: <PERSON>
# Tema: Student Performance Factors
# (1) Aplicar os cálculos vistos em aula:

file.choose()
df_csv = read.csv(file.choose())
df_csv

#nome das colunas
colnames(df_csv)

#dimensão da tabela
dim(df_csv)

#medidas de tendência central
#média
mean(df_csv $ Exam_Score)
mean(df_csv $ Previous_Scores)

#mediana
median(df_csv $ Exam_Score)
median(df_csv $ Previous_Scores)

#moda
desem_nf<-df_csv $ Exam_Score
desem_nf

desem_na<-df_csv $ Previous_Scores
desem_na

f.desem_nf<-table(desem_nf)
f.desem_na<-table(desem_na)

fmax_nf<-max(f.desem_nf)
fmax_nf

fmax_na<-max(f.desem_na)
fmax_na

#encontrar o valor correspondente da frequência máxima
moda1<-(names(f.desem_nf[f.desem_nf == fmax_nf]))
moda1

moda2<-(names(f.desem_na[f.desem_na == fmax_na]))
moda2

#medidas de variabilidade
#variância
var(df_csv $ Exam_Score)
var(df_csv $ Previous_Scores)

#desvio-padrão
dp1<-sd(df_csv $ Exam_Score)
dp1

dp2<-sd(df_csv $ Previous_Scores)
dp2

#coeficiente de variação
coef_var1<-dp1/mean(df_csv $ Exam_Score)*100
coef_var1

coef_var2<-dp2/mean(df_csv $ Previous_Scores)*100
coef_var2

#medidas de posição
#sumarização de variáveis
summary(df_csv $ Exam_Score)
summary(df_csv $ Previous_Scores)

#percentil Exame Final
percentil1_1<-quantile((df_csv $ Exam_Score), probs = 0.01)
percentil1_1

percentil1_25<-quantile((df_csv $ Exam_Score), probs = 0.25)
percentil1_25

percentil1_50<-quantile((df_csv $ Exam_Score), probs = 0.50)
percentil1_50

percentil1_75<-quantile((df_csv $ Exam_Score), probs = 0.75)
percentil1_75

percentil1_100<-quantile((df_csv $ Exam_Score), probs = 1)
percentil1_100

#percentil Exames Anteriores
percentil2_1<-quantile((df_csv $ Previous_Scores), probs = 0.01)
percentil2_1

percentil2_25<-quantile((df_csv $ Previous_Scores), probs = 0.25)
percentil2_25

percentil2_50<-quantile((df_csv $ Previous_Scores), probs = 0.50)
percentil2_50

percentil2_75<-quantile((df_csv $ Previous_Scores), probs = 0.75)
percentil2_75

percentil2_100<-quantile((df_csv $ Previous_Scores), probs = 1)
percentil2_100

# "Os valores das métricas indicam que os estudantes apresentaram um desempenho superior nos exames anteriores (Previous_Scores: média 75,07, mediana 75, moda 66) em comparação ao exame final (Exam_Score: média 67,24, mediana 67, moda 68); o que revela que o desempenho diminuiu no exame final, provavelmente por dificuldades no aprendizado e/ou presença na aula. A variância mais alta nas notas anteriores (Previous_Scores: 207,35 e Exam_Score: 68) sugere uma grande dispersão nos resultados dos exames anteriores, enquanto o exame final apresentou um desempenho mais concentrado, mas inferior, o que é confirmado pelos desvios padrão (Previous_Scores: 14,40; Exam_Score: 3,89) e pelos coeficientes de variação (Previous_Scores: 19%; Exam_Score: 5,8%). Os quartis e percentis reforçam que os resultados das avaliações anteriores variaram mais, com desempenhos destacados, enquanto no exame final o desempenho foi mais uniforme e menor. Para melhorar o desempenho no exame final, considerando o conjunto de dados e suas colunas, seria interessante reforçar a preparação contínua, incentivar a participação/presença nas aulas e dispor de tutoria aos alunos que têm dificuldades."
