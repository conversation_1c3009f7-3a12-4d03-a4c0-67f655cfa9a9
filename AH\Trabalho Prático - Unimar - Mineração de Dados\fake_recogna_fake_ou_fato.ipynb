{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["!pip install nltk --upgrade -q"], "metadata": {"id": "vuimiz6Evq4i"}, "execution_count": 1, "outputs": []}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "QlEL1f-6mnvN", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "9b327507-c28b-4fdd-b358-06828881c536"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Cloning into 'FakeRecogna'...\n", "remote: Enumerating objects: 36, done.\u001b[K\n", "remote: Counting objects: 100% (36/36), done.\u001b[K\n", "remote: Compressing objects: 100% (30/30), done.\u001b[K\n", "remote: Total 36 (delta 7), reused 33 (delta 5), pack-reused 0 (from 0)\u001b[K\n", "Receiving objects: 100% (36/36), 31.19 MiB | 13.10 MiB/s, done.\n", "Resolving deltas: 100% (7/7), done.\n"]}], "source": ["!git clone https://github.com/<PERSON>/FakeRecogna.git"]}, {"cell_type": "code", "source": ["import pandas as pd"], "metadata": {"id": "0oy4PF8LegM0"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["df = pd.read_excel(\"/content/FakeRecogna/dataset/FakeRecogna.xlsx\")\n", "df.head(3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 421}, "id": "StcEcTApxz4k", "outputId": "29c45147-f0ff-4fa1-f63e-860cc110fbd5"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                              Titulo  \\\n", "0  \\n\\nPapa Francisco foi preso sob acusação de t...   \n", "1  Equador prepara cova coletiva para mortos por ...   \n", "2  Air France voltará a operar voo direto Pequim-...   \n", "\n", "                                           Subtitulo  \\\n", "0  Boato – Ocorreu um apagão no Vaticano. O papa ...   \n", "1                                                NaN   \n", "2                                                NaN   \n", "\n", "                                             Noticia       Categoria  \\\n", "0  apagão vaticano papar presar acusação tráfico ...  entretenimento   \n", "1  o governar equador anunciar preparar cova cole...           sa<PERSON>de   \n", "2  o companhia air france operar voar direto pequ...           saúde   \n", "\n", "                Data              Autor  \\\n", "0         11/01/2021  \\nEd<PERSON>    \n", "1  27/03/2020 18h25   27/03/2020 18h25    \n", "2  07/08/2020 13h42   07/08/2020 13h42    \n", "\n", "                                                 URL  Classe  \n", "0  https://www.boatos.org/religiao/papa-francisco...     0.0  \n", "1  https://noticias.uol.com.br/internacional/ulti...     1.0  \n", "2  https://www.uol.com.br/nossa/noticias/afp/2020...     1.0  "], "text/html": ["\n", "  <div id=\"df-6bcc4240-5a99-45ce-b506-fdc1a7d2f04c\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Titulo</th>\n", "      <th>Subti<PERSON><PERSON></th>\n", "      <th>Noticia</th>\n", "      <th>Categoria</th>\n", "      <th>Data</th>\n", "      <th>Autor</th>\n", "      <th>URL</th>\n", "      <th>Classe</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>\\n\\nPapa Francisco foi preso sob acusação de t...</td>\n", "      <td><PERSON><PERSON> – Ocorreu um apagão no Vaticano. O papa ...</td>\n", "      <td>apagão vaticano papar presar acusação tráfico ...</td>\n", "      <td>entretenimento</td>\n", "      <td>11/01/2021</td>\n", "      <td>\\nEdgard <PERSON></td>\n", "      <td>https://www.boatos.org/religiao/papa-francisco...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Equador prepara cova coletiva para mortos por ...</td>\n", "      <td>NaN</td>\n", "      <td>o governar equador anunciar preparar cova cole...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>27/03/2020 18h25</td>\n", "      <td>27/03/2020 18h25</td>\n", "      <td>https://noticias.uol.com.br/internacional/ulti...</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Air France voltará a operar voo direto Pequim-...</td>\n", "      <td>NaN</td>\n", "      <td>o companhia air france operar voar direto pequ...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>07/08/2020 13h42</td>\n", "      <td>07/08/2020 13h42</td>\n", "      <td>https://www.uol.com.br/nossa/noticias/afp/2020...</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-6bcc4240-5a99-45ce-b506-fdc1a7d2f04c')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-6bcc4240-5a99-45ce-b506-fdc1a7d2f04c button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-6bcc4240-5a99-45ce-b506-fdc1a7d2f04c');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-4c23947b-a8b4-4477-a237-d38dc7b130b2\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-4c23947b-a8b4-4477-a237-d38dc7b130b2')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-4c23947b-a8b4-4477-a237-d38dc7b130b2 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 11903,\n  \"fields\": [\n    {\n      \"column\": \"Titulo\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 11662,\n        \"samples\": [\n          \"Governo diz que pode atrasar para pagar, mas o Bolsa Fam\\u00edlia n\\u00e3o acabar\\u00e1  \",\n          \"\\n\\nCasamento de Preta Gil foi pago com a Lei Rouanet #boato \\n\",\n          \"Aprova\\u00e7\\u00e3o emergencial de vacinas em outros pa\\u00edses n\\u00e3o pode embasar decis\\u00e3o da Anvisa, diz presidente  \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Subtitulo\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 5410,\n        \"samples\": [\n          \"Mensagens circulam ap\\u00f3s sumi\\u00e7o do vereador na web. Mas elas n\\u00e3o s\\u00e3o verdadeiras. Twitter diz n\\u00e3o ter tomado qualquer medida contra o vereador. Facebook afirma que vers\\u00e3o que circula \\u00e9 falsa. Tamb\\u00e9m n\\u00e3o h\\u00e1 nenhuma decis\\u00e3o neste sentido por parte do ministro do STF.\",\n          \"Onde antes ficava a pista de dan\\u00e7a, agora existem prateleiras com comidas, bebidas e verduras, e um DJ que toca as m\\u00fasicas que os clientes pedem enquanto fazem compras.\",\n          \"Boato \\u2013 Um morador da \\u201csua cidade\\u201d \\u00e9 o mais novo milion\\u00e1rio da Mega-Sena. Ele ganhou o pr\\u00eamio acumulado e todos os detalhes voc\\u00ea pode ver no link.\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Noticia\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 11886,\n        \"samples\": [\n          \"\\u201c ler agradacer o deus vidar e pa\\u00eds triste o noticiar midia espalhar mundo inteirar nova_yorque abrir valar comun deitar pessoa morto coronavirus lamentar situa\\u00e7\\u00e3o crist\\u00e3o sic \\u201d indicar o legendar publica\\u00e7\\u00e3o facebook compartilhar o dia abril d haver postagens circular n redar social espanhol associar o o coronav\\u00edrus o indica\\u00e7\\u00e3o tratar pessoa respeitar o quarentenar equador buscar reverso foto motor google e yandex mostrar registro comunica\\u00e7\\u00e3o indicar o imagem corresponder o tumulto ocorrer setembro ar\\u00e1bio sa\\u00fade durante peregrina\\u00e7\\u00e3o meco o fotografia aparecer cr\\u00e9dito blog africanar registrar o incidente\",\n          \"O m\\u00e9dico Anthony Fauci, principal conselheiro em epidemiologia na Casa Branca, afirmou nesta ter\\u00e7a-feira (30) que o novo v\\u00edrus descoberto em porcos na China se assemelha aos v\\u00edrus de gripe que causaram as pandemias de 2008 e 1918.  Assim como os v\\u00edrus da chamada \\\"gripe su\\u00edna\\\" e da \\\"gripe espanhola\\\", esse novo influenza \\u00e9 uma variante do H1N1 \\u2014 est\\u00e1 sendo chamado por cientistas G4 EA H1N1. A descoberta vem de estudos que levaram anos em abatedouros chineses, e, por enquanto, N\\u00c3O h\\u00e1 relatos de transmiss\\u00e3o entre humanos (leia mais no fim da reportagem).  Fauci explicou a parlamentares no Congresso dos Estados Unidos qual o risco desse novo v\\u00edrus de gripe \\u00e0 sa\\u00fade humana. \\\"Em outras palavras, quando voc\\u00ea tem um novo v\\u00edrus que se torna um v\\u00edrus pand\\u00eamico \\u00e9 porque sofreu muta\\u00e7\\u00f5es e/ou por reorganiza\\u00e7\\u00e3o ou mudan\\u00e7as de genes\\\", afirmou.  O estudo sobre o novo v\\u00edrus influenza foi publicado na segunda-feira na revista cient\\u00edfica \\\"Proceedings of the National Academy of Sciences\\\" por pesquisadores da Universidade Agricultural Chinesa com an\\u00e1lises em mais de 30 mil porcos entre 2011 e 2018. Veja mais no V\\u00cdDEO acima.\",\n          \"circular rede social imagem soldar ex\\u00e9rcito trabalhar obrar transposi\\u00e7\\u00e3o rir francisco legendar acompanhar o imagem feito durante o governar bolsonaro o m\\u00eddia esconder informa\\u00e7\\u00e3o o imagem feito durante o governar bolsonaro durante o gest\\u00e3o dilma rousseff haver diverso registro imagem internet ano anterior o o governar bolsonaro o ex\\u00e9rcito o imagem feito durante trabalho florestar pe 2011/2012 o 1\\u00ba grupamento engenhar ex\\u00e9rcito informar o imagem corresponder constru\\u00e7\\u00e3o valeta drenagem externo canal aproxima\\u00e7\\u00e3o eixo ler projeto integra\\u00e7\\u00e3o rir francisco trabalhar executar militar ex\\u00e9rcito precisamente 3\\u00ba batalh\\u00e3o engenhar constru\\u00e7\\u00e3o noto\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Categoria\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 6,\n        \"samples\": [\n          \"entretenimento\",\n          \"sa\\u00fade\",\n          \"pol\\u00edtica\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Data\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 8787,\n        \"samples\": [\n          \"06/04/2020 18h30 \",\n          \"15/01/2021 06h04 \",\n          \"01/04/2020 14h41Atualizada em 01/04/2020 15h10 \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Autor\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4405,\n        \"samples\": [\n          \"Daniel Weterman e Rafael Moraes Moura\",\n          \"Renata Cafardo e J\\u00falia Marques\",\n          \"25/06/2020 11h39 \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"URL\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 11675,\n        \"samples\": [\n          \"https://noticias.uol.com.br/internacional/ultimas-noticias/2021/01/29/agencia-europeia-de-medicamentos-recomenda-autorizacao-da-vacina-de-oxford.htm\",\n          \"https://www.uol.com.br/universa/noticias/redacao/2020/04/09/gestantes-e-puerperas-entram-no-grupo-de-risco-para-a-covid-19-entenda.htm\",\n          \"https://checamos.afp.com//franca-incluiu-na-lei-um-novo-criterio-para-interrupcao-medica-da-gravidez-ja-possivel-em-toda\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Classe\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.5000210061968373,\n        \"min\": 0.0,\n        \"max\": 1.0,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1.0,\n          0.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["df.info()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ol9YK11Zeukl", "outputId": "fbdfae92-6a21-4d9f-e938-cd9c7d53c86e"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 11903 entries, 0 to 11902\n", "Data columns (total 8 columns):\n", " #   Column     Non-Null Count  Dtype  \n", "---  ------     --------------  -----  \n", " 0   Titulo     11872 non-null  object \n", " 1   Subtitulo  5580 non-null   object \n", " 2   Noticia    11902 non-null  object \n", " 3   Categoria  11902 non-null  object \n", " 4   Data       11551 non-null  object \n", " 5   Autor      11886 non-null  object \n", " 6   URL        11902 non-null  object \n", " 7   Classe     11902 non-null  float64\n", "dtypes: float64(1), object(7)\n", "memory usage: 744.1+ KB\n"]}]}, {"cell_type": "code", "source": ["import pandas as pd\n", "import re\n", "import nltk\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from nltk.stem import WordNetLemmatizer\n", "from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier, VotingClassifier\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from xgboost import XGBClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score, roc_curve\n", "from sklearn.cluster import KMeans\n", "from collections import Counter\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import shap\n", "import joblib\n", "\n", "material_palette = [\"#1E88E5\", \"#D81B60\", \"#FFC107\", \"#43A047\", \"#8E24AA\", \"#F4511E\"]\n", "sns.set_palette(material_palette)"], "metadata": {"id": "4Dv9C8LtokFb"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["# recursos do NLTK.\n", "nltk.download('punkt')\n", "nltk.download('stopwords')\n", "nltk.download('wordnet')\n", "nltk.download('punkt_tab')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tAfRxV_moj2t", "outputId": "6b084a11-d4ea-49f8-ba78-4519f6b62c48"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt.zip.\n", "[nltk_data] Downloading package stopwords to /root/nltk_data...\n", "[nltk_data]   Unzipping corpora/stopwords.zip.\n", "[nltk_data] Downloading package wordnet to /root/nltk_data...\n", "[nltk_data] Downloading package punkt_tab to /root/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt_tab.zip.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["print(\"num  vlr ausentes antes\", df['Classe'].isna().sum())\n", "df = df.dropna(subset=['Classe'])\n", "print(\"num  vlr ausentes depois\", df['Classe'].isna().sum())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "suFtHv60pxdk", "outputId": "86030911-5abc-4051-c11b-cf40d9e34cc3"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["num  vlr ausentes antes 1\n", "num  vlr ausentes depois 0\n"]}]}, {"cell_type": "code", "source": ["# função para limpar e pré-processar o texto.\n", "def preprocess_text(text):\n", "    \"\"\"\n", "    Pré-processa o texto: remove URLs, caracteres especiais, números, stopwords e aplica lematização.\n", "    \"\"\"\n", "    if pd.isna(text):\n", "        return ''\n", "\n", "    text = re.sub(r'http\\S+|www\\S+|https\\S+', '', text, flags=re.MULTILINE)\n", "    text = re.sub(r'\\W+|\\d+', ' ', text)\n", "    text = text.lower()\n", "\n", "    tokens = word_tokenize(text)\n", "\n", "    stop_words = set(stopwords.words('portuguese'))\n", "    custom_stopwords = {\"dia\", \"ano\", 'd', 'ser', 'ter', 'fazer'}  # adicionando mais stopwords.\n", "    stop_words.update(custom_stopwords)\n", "    tokens = [word for word in tokens if word not in stop_words]\n", "\n", "    lemmatizer = WordNetLemmatizer()\n", "    tokens = [lemmatizer.lemmatize(word) for word in tokens]\n", "\n", "    return ' '.join(tokens)"], "metadata": {"id": "dBOzfFBOojyg"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["# função para avaliar o modelo.\n", "def evaluate_model(model, X_test, y_test, model_name):\n", "    \"\"\"\n", "    Avalia o modelo e exibe métricas de desempenho.\n", "    \"\"\"\n", "    y_pred = model.predict(X_test)\n", "    y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, \"predict_proba\") else None\n", "\n", "    acc = accuracy_score(y_test, y_pred)\n", "    prec = precision_score(y_test, y_pred)\n", "    rec = recall_score(y_test, y_pred)\n", "    f1 = f1_score(y_test, y_pred)\n", "    auc = roc_auc_score(y_test, y_pred_proba) if y_pred_proba is not None else None\n", "\n", "    print(f\"\\nAvaliação do modelo {model_name}:\")\n", "    print(f\"Acurácia: {acc:.4f}\")\n", "    print(f\"Precisão: {prec:.4f}\")\n", "    print(f\"Recall: {rec:.4f}\")\n", "    print(f\"F1-Score: {f1:.4f}\")\n", "    print(f\"AUC-ROC: {auc:.4f}\" if auc is not None else \"\")\n", "    print(f\"<PERSON><PERSON>:\\n{confusion_matrix(y_test, y_pred)}\")\n", "\n", "    # plota matriz de confusão.\n", "    sns.heatmap(confusion_matrix(y_test, y_pred), annot=True, fmt='d')\n", "    plt.title(f'<PERSON><PERSON> - {model_name}')\n", "    plt.show()\n", "\n", "    # plota curva ROC.\n", "    if y_pred_proba is not None:\n", "        fpr, tpr, _ = roc_curve(y_test, y_pred_proba)\n", "        plt.plot(fpr, tpr, label=f\"AUC = {auc:.4f}\")\n", "        plt.xlabel(\"Taxa de Falso Positivo\")\n", "        plt.ylabel(\"Taxa de Verdadeiro Positivo\")\n", "        plt.title(f\"Curva ROC - {model_name}\")\n", "        plt.legend()\n", "        plt.show()\n", "\n", "    return acc, prec, rec, f1, auc"], "metadata": {"id": "TbBXVeHVojws"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["# função para treinar e avaliar modelos com validação cruzada estratificada.\n", "def train_and_evaluate(model, X_train, y_train, X_test, y_test, model_name, results, trained_models):\n", "    \"\"\"\n", "    Treina o modelo com validação cruzada estratificada e avalia no conjunto de teste.\n", "    Armazena os resultados e o modelo treinado para comparação posterior.\n", "    \"\"\"\n", "    print(f\"\\nTreinando {model_name}...\")\n", "    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "    cv_scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='accuracy')\n", "    print(f\"Acur<PERSON>cia média ({model_name} - validação cruzada): {cv_scores.mean():.4f}\")\n", "\n", "    model.fit(X_train, y_train)\n", "    acc, prec, rec, f1, auc = evaluate_model(model, X_test, y_test, model_name)\n", "\n", "    # armazena os resultados do modelo.\n", "    results.append({\n", "        \"Modelo\": model_name,\n", "        \"Acurácia\": acc,\n", "        \"Precisão\": prec,\n", "        \"Recall\": rec,\n", "        \"F1-Score\": f1,\n", "        \"AUC-ROC\": auc\n", "    })\n", "\n", "    # armazena o modelo treinado.\n", "    trained_models[model_name] = model"], "metadata": {"id": "QyxyKWTPorOO"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["# dividi os dados em treino e teste (80/20).\n", "train_df, test_df = train_test_split(df, test_size=0.2, random_state=42)\n", "print(f\"conjunto de treino: {len(train_df)}\")\n", "print(f\"conjunto de teste: {len(test_df)}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zCLCwETBoq_k", "outputId": "1eb712dc-84e8-4eea-88be-7d3663ec77e6"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["conjunto de treino: 9521\n", "conjunto de teste: 2381\n"]}]}, {"cell_type": "code", "source": ["# substitui valores NaN por strings vazias na coluna 'Noticia'.\n", "train_df['Noticia'] = train_df['Noticia'].fillna('')\n", "test_df['Noticia'] = test_df['Noticia'].fillna('')"], "metadata": {"id": "nsNm_4Tfoq97"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["# aplica a função de pré-processamento às colunas de texto.\n", "train_df['Noticia_processed'] = train_df['Noticia'].apply(preprocess_text)\n", "test_df['Noticia_processed'] = test_df['Noticia'].apply(preprocess_text)\n", "\n", "print(\"\\ntexto pré-processado:\")\n", "print(train_df['Noticia_processed'].head())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KdDWhXGypfbU", "outputId": "143d0e07-eaef-46ed-f3ec-f10eec1b37f1"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "texto pré-processado:\n", "9768                      trump presidente voltar futurar\n", "416     testar popularidade confiar pesquisar datafolh...\n", "3578    basear cálculo relacionar umar mudança calendá...\n", "4600    texto amplamente compartilhar whatsapp quinzen...\n", "1962    justiçar federal autorizar hoje governar distr...\n", "Name: Noticia_processed, dtype: object\n"]}]}, {"cell_type": "code", "source": ["# cria o vetorizador TF-IDF com n-grams.\n", "tfidf_vectorizer = TfidfVectorizer(max_features=5000, ngram_range=(1, 2))\n", "X_train = tfidf_vectorizer.fit_transform(train_df['Noticia_processed'])\n", "X_test = tfidf_vectorizer.transform(test_df['Noticia_processed'])"], "metadata": {"id": "Q_CPlCNipfP4"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["# labels.\n", "y_train = train_df['Classe']\n", "y_test = test_df['Classe']"], "metadata": {"id": "OlwlG6GwpfLx"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["# armazena os resultados dos modelos.\n", "results = []\n", "trained_models = {}  # armazena os modelos treinados."], "metadata": {"id": "j1QMJYrNpfKH"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["# treina e avalia modelos de (Regressão Logística, KNN, XGBoost, Random Forest).\n", "train_and_evaluate(LogisticRegression(max_iter=1000), X_train, y_train, X_test, y_test, \"Regressão Logística\", results, trained_models)\n", "train_and_evaluate(RandomForestClassifier(n_estimators=100, random_state=42), X_train, y_train, X_test, y_test, \"Random Forest\", results, trained_models)\n", "train_and_evaluate(KNeighborsClassifier(), X_train, y_train, X_test, y_test, \"KNN\", results, trained_models)\n", "train_and_evaluate(XGBClassifier(random_state=42, eval_metric='logloss'), X_train, y_train, X_test, y_test, \"XGBoost\", results, trained_models)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "ndoKWXtrqc-A", "outputId": "5356df94-6efd-4bcb-f30d-218cc82eac24"}, "execution_count": 18, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Treinando Regressão Logística...\n", "<PERSON>cur<PERSON><PERSON> média (Regressão Logística - validação cruzada): 0.9355\n", "\n", "Avaliação do modelo Regressão Logística:\n", "Acurácia: 0.9269\n", "Precisão: 0.9179\n", "Recall: 0.9406\n", "F1-Score: 0.9291\n", "AUC-ROC: 0.9766\n", "<PERSON><PERSON>:\n", "[[1067  102]\n", " [  72 1140]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAhAAAAG0CAYAAABjZR7sAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAQthJREFUeJzt3Xd8FNX+//H3ppCEVEK6tIBIiCBVITRFohERBVGMIAICUSl+6cpFiopE4CpKFwuggCIqFhCkiKASaYoivSNgEloSCBAgmd8f3uyPnSQMq4sJ+HrexzyuOXN25sxmN3z28zln1mYYhiEAAAAnuBX3AAAAwLWHAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIASrjdu3dr5MiR2rFjR3EPBbAjgCiBRo4cKZvNdlXPYbPZNHLkyKt6jn/auHHjVLlyZbm7u6t27dpX5RwDBw6Uv7+/OnfurBMnTig2NlabNm26KufCldu5c6eio6MVHR2tr776SnPnzlWbNm2Ke1guYRiGunbtqjVr1qhq1ap/61j79++XzWbTzJkzXTM4/Kv9qwOImTNnymazyWaz6fvvvy+w3zAMlS9fXjabTffdd99fOsfo0aP12Wef/c2RXhtyc3M1Y8YM3XHHHQoODpaXl5cqVaqkrl27asOGDVf13EuXLtXgwYPVuHFjzZgxQ6NHj3b5OU6fPq2pU6fqxRdf1JYtWxQSEiI/Pz/dcsstLj/Xlcp//eZvAQEBuv3227Vo0aJiG1NxePvtt1WzZk21a9dODz30kDp37qwuXbq49BxdunSRn5+fS495JSZPnqy9e/dqzpw5cnO7sj/Zc+fO1euvv351BwYY/2IzZswwJBne3t7G008/XWD/ypUrDUmGl5eX0apVq790Dl9fX6Nz585OPebChQvG2bNn/9L5rpQkY8SIES473pkzZ4x77rnHkGQ0a9bMGDdunPHOO+8Yw4YNM6pVq2bYbDbj999/d9n5zJ599lnDzc3NyMnJuWrnuHDhgrF//377z4cPHzZyc3Ov2vmuhCTjrrvuMt5//33jvffeM1566SUjKirKsNlsxpIlS4p1bP+ktLQ0IysryzAMwzh16pRx4sQJl5+jc+fOhq+vr8uPezkHDhwwgoKCjNWrVzv1uFatWhkVK1Ys0J6Xl2ecPXvWuHjxootGiH8zj2KNXkqIe++9V/Pnz9eECRPk4fH/n5K5c+eqXr16Onbs2D8yjuzsbPn6+srDw8NhHNeCQYMGacmSJRo/frz69u3rsG/EiBEaP378VT1/enq6fHx8VKpUqat2Dg8PD1WsWNH+c1RU1FU7lzNuuukmPfbYY/af27Vrp9jYWL3xxhtKSEj4R8eS/xr+p4WFhdn/uziyBFdLhQoVdPLkSZcdz2azydvb22XHw7/bv7qEke/RRx/V8ePHtWzZMnvb+fPn9fHHH6tDhw6FPua///2vGjVqpLJly8rHx0f16tXTxx9/7NDHZrMpOztbs2bNsqeY89Oq+fMctm7dqg4dOqhMmTJq0qSJw758Xbp0KZCqzt+s5jHk5OSoX79+Cg0Nlb+/v+6//34dOnSo0L6HDx/WE088ofDwcHl5eenmm2/Wu+++a/X06dChQ3rzzTd11113FQgeJMnd3V0DBw5UuXLl7G0///yzWrZsqYCAAPn5+alFixb68ccfHR6XX2L64Ycf1L9/f4WGhsrX11dt27bV0aNH7f1sNptmzJih7Oxs+/Myc+bMy9Z7zc/dqVOn1LdvX1WqVEleXl4KCwvTXXfdpZ9++sne59tvv9VDDz2kChUqyMvLS+XLl1e/fv109uzZAsf/5ptv1LRpU/n6+iooKEgPPPCAtm3bZvlcukL16tUVEhKiPXv2OLTn5ORoxIgRuvHGG+3jHzx4sHJychz6nT17Vs8884xCQkLsr5nDhw8XeM4u9xqWpNmzZ6tevXry8fFRcHCwEhMT9fvvvzuca9euXWrXrp0iIiLk7e2tcuXKKTExUZmZmfY+y5YtU5MmTRQUFCQ/Pz9Vq1ZN//nPf+z7z507p2HDhqlu3boKDAyUr6+vmjZtqpUrVxZ4brKzszVgwACVL19eXl5eqlatmv773//KcOGXEs+fP99+3SEhIXrsscd0+PDhQvvFxsbK29tbNWrU0IIFC9SlSxdVqlTJoZ+zr9U77rhDixYt0oEDB+zvh/xjFvWe2L59u9q3b6/Q0FD5+PioWrVqGjp0qH3/gQMH1LNnT1WrVk0+Pj4qW7asHn74Ye3fv98VTxmuUdfWx9yrpFKlSoqLi9MHH3ygli1bSpIWL16szMxMJSYmasKECQUe88Ybb+j+++9Xx44ddf78eX344Yd6+OGHtXDhQrVq1UqS9P7776t79+667bbblJSUJEmqUqWKw3EefvhhVa1aVaNHjy7yj9iTTz6p+Ph4h7YlS5Zozpw5Dp+8CtO9e3fNnj1bHTp0UKNGjfTNN9/Yx3eptLQ0NWzYUDabTb1791ZoaKgWL16sbt26KSsrq9DAIN/ixYt18eJFderU6bJjybdlyxY1bdpUAQEBGjx4sDw9PfXmm2/qjjvu0KpVq9SgQQOH/n369FGZMmU0YsQI7d+/X6+//rp69+6tefPmSfrzeZ4+fbrWrVunt99+W5LUqFGjKxpLvqeeekoff/yxevfurdjYWB0/flzff/+9tm3bprp160qSPvroI509e1Y9e/ZUcHCw1q1bp4kTJ+rQoUOaP3++/VjLly9Xy5YtVblyZY0cOVJnz57VxIkT1bhxY/30008F/oFwtczMTJ08edLhtZaXl6f7779f33//vZKSklS9enVt3rxZ48eP186dOx3m6XTp0kUfffSROnXqpIYNG2rVqlWFvmbyFfYafvnllzVs2DC1b99e3bt319GjRzVx4kQ1a9ZMP//8s4KCgnT+/HklJCQoJydHffr0UUREhA4fPqyFCxcqIyNDgYGB2rJli+677z7dcsstevHFF+Xl5aXdu3frhx9+sJ8/IyND77zzjh599FElJSUpKytL7777rhISErRu3Tr7hFrDMHT//fdr5cqV6tatm2rXrq2vv/5agwYN0uHDh12SJZs5c6a6du2qW2+9VcnJyUpLS9Mbb7yhH374wX7dkrRo0SI98sgjqlmzppKTk3Xy5El169ZNN9xwg+U5rF6rQ4cOVWZmpg4dOmS/pstlZX799Vc1bdpUnp6eSkpKUqVKlbRnzx59+eWXevnllyVJ69ev15o1a5SYmKhy5cpp//79mjp1qu644w5t3bpVpUuX/tvPHa5BxVtBKV75cyDWr19vTJo0yfD39zfOnDljGIZhPPzww0bz5s0NwzCMihUrFpgDkd8v3/nz540aNWoYd955p0N7UXMgRowYYUgyHn300SL3FWXXrl1GYGCgcdddd122lrlp0yZDktGzZ0+H9g4dOhSYA9GtWzcjMjLSOHbsmEPfxMREIzAwsMD1Xqpfv36GJOPnn38uss+l2rRpY5QqVcrYs2ePve3IkSOGv7+/0axZM3tb/u8nPj7eyMvLczifu7u7kZGRYW8rrD69b98+Q5IxY8aMAmMwX39gYKDRq1evy447Ozu7QFtycrJhs9mMAwcO2Ntq165thIWFGcePH7e3/fLLL4abm5vx+OOPX/YczpJkdOvWzTh69KiRnp5ubNiwwT4XZdy4cfZ+77//vuHm5mZ89913Do+fNm2aIcn44YcfDMMwjI0bNxqSjL59+zr069KlS4HnrKjX8P79+w13d3fj5ZdfdmjfvHmz4eHhYW//+eefDUnG/Pnzi7y+8ePHG5KMo0ePFtnnwoULBea+nDx50ggPDzeeeOIJe9tnn31mSDJGjRrl0Pehhx4ybDabsXv37iLPYRjWcyDOnz9vhIWFGTVq1HCYw7Rw4UJDkjF8+HB7W82aNY1y5coZp06dsrd9++23hqQCcxf+ymu1qDkQhb0nmjVrZvj7+zu8hg3DcHjPFfb+T0lJMSQZ77333mXHgusXJYz/ad++vc6ePauFCxfq1KlTWrhwYZHlC0ny8fGx//fJkyeVmZmppk2bOqS8r8RTTz3lVP/s7Gy1bdtWZcqU0QcffCB3d/ci+3711VeSpGeeecah3ZxNMAxDn3zyiVq3bi3DMHTs2DH7lpCQoMzMzMteV1ZWliTJ39/fcvy5ublaunSp2rRpo8qVK9vbIyMj1aFDB33//ff24+VLSkpyKOk0bdpUubm5OnDggOX5rlRQUJDWrl2rI0eOFNnn0k9Z2dnZOnbsmBo1aiTDMPTzzz9Lkv744w9t2rRJXbp0UXBwsL3/Lbfcorvuusv+O3Gld955R6GhoQoLC1P9+vW1YsUKDR48WP3797f3mT9/vqpXr66YmBiH3++dd94pSfZ0/5IlSyRJPXv2dDhHnz59ijy/+TX86aefKi8vT+3bt3c4V0REhKpWrWo/V2BgoCTp66+/1pkzZwo9dv4n9s8//1x5eXmF9vHw8LDPfcnLy9OJEyd08eJF1a9f3+F1+9VXX8nd3b3A+2HAgAEyDEOLFy8u8hqvxIYNG5Senq6ePXs6zDNo1aqVYmJi7Ctjjhw5os2bN+vxxx93yAzcfvvtqlmzpuV5ruS1eqWOHj2q1atX64knnlCFChUc9l36nrv0792FCxd0/Phx3XjjjQoKCnL6bx6uHwQQ/xMaGqr4+HjNnTtXn376qXJzc/XQQw8V2X/hwoVq2LChvL29FRwcrNDQUE2dOtWhdnsloqOjnerfo0cP7dmzRwsWLFDZsmUv2/fAgQNyc3MrUDapVq2aw89Hjx5VRkaGpk+frtDQUIeta9eukv6cpFiUgIAASX/WZq0cPXpUZ86cKTAG6c/afV5eXoE6ufkPW5kyZSTJpZPLxo4dq99++03ly5fXbbfdppEjR2rv3r0OfQ4ePGgPDPz8/BQaGqrbb79dkuy/9/ygpqjrO3bsmLKzs4scR2pqqsNW2PwKswceeEDLli3TokWL7PMSzpw547Dkb9euXdqyZUuB3+9NN90k6f//fvNfM+bX5Y033ljk+c19d+3aJcMwVLVq1QLn27Ztm/1c0dHR6t+/v95++22FhIQoISFBkydPdngPPfLII2rcuLG6d++u8PBwJSYm6qOPPioQTMyaNUu33HKLvL29VbZsWYWGhmrRokUOxzpw4ICioqIKBLrVq1e37/87Lve7j4mJse/P///CntPLPc/5ruS1eqXyH1ejRo3L9jt79qyGDx9unzsSEhKi0NBQZWRkOP03D9cP5kBcokOHDurRo4dSU1PVsmVL+6cfs++++07333+/mjVrpilTpigyMlKenp6aMWOG5s6d69Q5L43srbzxxhv64IMPNHv2bJfeKCn/j/Fjjz2mzp07F9rncvc6iImJkSRt3rz5qtzAqagsi2Ex8a2om3Hl5uYWaGvfvr2aNm2qBQsWaOnSpRo3bpzGjBmjTz/9VC1btlRubq7uuusunThxQs8++6xiYmLk6+urw4cPq0uXLkV+OnZWZGSkw88zZsywvJ9BuXLl7HNk7r33XoWEhKh3795q3ry5HnzwQUl//o5r1qyp1157rdBjlC9f/i+P2fwazsvLk81m0+LFiwv93V36qfvVV19Vly5d9Pnnn2vp0qV65plnlJycrB9//FHlypWTj4+PVq9erZUrV2rRokVasmSJ5s2bpzvvvFNLly6Vu7u7Zs+erS5duqhNmzYaNGiQwsLC5O7uruTk5AITSa8HVq/Vq6FPnz6aMWOG+vbtq7i4OAUGBspmsykxMdFlr31cewggLtG2bVs9+eST+vHHH+0T9ArzySefyNvbW19//bW8vLzs7TNmzCjQ11V3lPzuu+80cOBA9e3bVx07dryix1SsWFF5eXnas2ePw6ci8+1w81do5ObmFpiseSVatmxp/0NuNZEyNDRUpUuXLvSWvNu3b5ebm9vf+sfsUvmZioyMDIf2oj5pRkZGqmfPnurZs6fS09NVt25dvfzyy2rZsqU2b96snTt3atasWXr88cftj7l05Y4k+zLPoq4vJCTksssczce7+eabi77AIjz55JMaP368nn/+ebVt21Y2m01VqlTRL7/8ohYtWlz2NZn/mtm3b5/DXQ937959xeevUqWKDMNQdHS0PcNxOTVr1lTNmjX1/PPPa82aNWrcuLGmTZumUaNGSZLc3NzUokULtWjRQq+99ppGjx6toUOHauXKlYqPj9fHH3+sypUr69NPP3W4thEjRhS4tuXLl+vUqVMOWYjt27fb9/8dl/7u80tD+Xbs2GHfn///hT2nV/o8X+61Kl353538MuJvv/122X4ff/yxOnfurFdffdXedu7cuQLvLfy7UMK4hJ+fn6ZOnaqRI0eqdevWRfZzd3eXzWZz+CS7f//+Qu846evr+7ffZH/88Yfat2+vJk2aaNy4cVf8uPw/JuZVJOY71Lm7u6tdu3b65JNPCv1DcumSycKUL19ePXr00NKlSzVx4sQC+/Py8vTqq6/q0KFDcnd31913363PP//cYQlYWlqa5s6dqyZNmthLIn9XQECAQkJCtHr1aof2KVOmOPycm5tbIA0bFhamqKgo+xLH/E/Sl2Y9DMPQG2+84fC4yMhI1a5dW7NmzXL4vf/2229aunSp7r333suOOT4+3mEzZySuhIeHhwYMGKBt27bp888/l/Tnp9bDhw/rrbfeKtD/7Nmz9rJK/n0jzM9RYb/Xojz44INyd3fXCy+8UCBLZBiGjh8/LunPuTMXL1502F+zZk25ubnZn/cTJ04UOH5+lutyv5u1a9cqJSXF4XH33nuvcnNzNWnSJIf28ePHy2az/e1P7/Xr11dYWJimTZvmsDR28eLF2rZtm30lS1RUlGrUqKH33ntPp0+ftvdbtWqVNm/efNlzXMlrVfrz786VlBZCQ0PVrFkzvfvuuzp48KDDvkufT3d39wK/y4kTJxaazcO/BxkIk6JS+Jdq1aqVXnvtNd1zzz3q0KGD0tPTNXnyZN1444369ddfHfrWq1dPy5cv12uvvaaoqChFR0cXWKZo5ZlnntHRo0c1ePBgffjhhw77brnlliLLC7Vr19ajjz6qKVOmKDMzU40aNdKKFSsK/ZTzyiuvaOXKlWrQoIF69Oih2NhYnThxQj/99JOWL19e6B/yS7366qvas2ePnnnmGX366ae67777VKZMGR08eFDz58/X9u3blZiYKEkaNWqUfW1/z5495eHhoTfffFM5OTkaO3asU8+Nle7du+uVV15R9+7dVb9+fa1evVo7d+506HPq1CmVK1dODz30kGrVqiU/Pz8tX75c69evt3/iiomJUZUqVTRw4EAdPnxYAQEB+uSTTwqdhzFu3Di1bNlScXFx6tatm30ZZ2Bg4D/2/SNdunTR8OHDNWbMGLVp00adOnXSRx99pKeeekorV65U48aNlZubq+3bt+ujjz7S119/rfr166tevXpq166dXn/9dR0/fty+jDP/ObuST7ZVqlTRqFGjNGTIEO3fv19t2rSRv7+/9u3bpwULFigpKUkDBw7UN998o969e+vhhx/WTTfdpIsXL+r999+3B7SS9OKLL2r16tVq1aqVKlasqPT0dE2ZMkXlypWz33Pivvvu06effqq2bduqVatW2rdvn6ZNm6bY2FiHf6Bbt26t5s2ba+jQodq/f79q1aqlpUuX6vPPP1ffvn0LzBUqzIULF+yZkUsFBwerZ8+eGjNmjLp27arbb79djz76qH0ZZ6VKldSvXz97/9GjR+uBBx5Q48aN1bVrV508eVKTJk1SjRo1HMZsdiWvVenPvzvz5s1T//79deutt8rPz6/ID0UTJkxQkyZNVLduXSUlJSk6Olr79+/XokWL7N/zct999+n9999XYGCgYmNjlZKSouXLl1vOw8J1rjiWfpQUly7jvJzClnG+8847RtWqVQ0vLy8jJibGmDFjRqHLL7dv3240a9bM8PHxMSTZl3Tm9y1seZr5OLfffrshqdDN6nbUZ8+eNZ555hmjbNmyhq+vr9G6dWvj999/L/SxaWlpRq9evYzy5csbnp6eRkREhNGiRQtj+vTplz1HvosXLxpvv/220bRpUyMwMNDw9PQ0KlasaHTt2rXAEs+ffvrJSEhIMPz8/IzSpUsbzZs3N9asWePQp6jfT/4txleuXGlvK2qJ3ZkzZ4xu3boZgYGBhr+/v9G+fXsjPT3d4fpzcnKMQYMGGbVq1TL8/f0NX19fo1atWsaUKVMcjrV161YjPj7e8PPzM0JCQowePXoYv/zyS6FLRZcvX240btzY8PHxMQICAozWrVsbW7duvaLn0RmSilzSN3LkSIfn6fz588aYMWOMm2++2fDy8jLKlClj1KtXz3jhhReMzMxM++Oys7ONXr16GcHBwYafn5/Rpk0bY8eOHYYk45VXXrH3u9xr2DAM45NPPjGaNGli+Pr6Gr6+vkZMTIzRq1cvY8eOHYZhGMbevXuNJ554wqhSpYrh7e1tBAcHG82bNzeWL19uP8aKFSuMBx54wIiKijJKlSplREVFGY8++qixc+dOe5+8vDxj9OjRRsWKFQ0vLy+jTp06xsKFC43OnTsXWMp46tQpo1+/fkZUVJTh6elpVK1a1Rg3bpzDksWidO7cucj3YZUqVez95s2bZ9SpU8fw8vIygoODjY4dOxqHDh0qcLwPP/zQiImJMby8vIwaNWoYX3zxhdGuXTsjJibGod9fea2ePn3a6NChgxEUFOSwNLSopc2//fab0bZtWyMoKMjw9vY2qlWrZgwbNsy+/+TJk0bXrl2NkJAQw8/Pz0hISDC2b99uVKxY0elb9eP6YTMMF96CDcB1adOmTapTp45mz559xXNw4LzatWsrNDS0wFwYoCRiDgQAB4UtHX399dfl5uamZs2aFcOIrj8XLlwoMP/j22+/1S+//KI77rijeAYFOIk5EAAcjB07Vhs3blTz5s3l4eGhxYsXa/HixUpKSnLZCpl/u8OHDys+Pl6PPfaYoqKitH37dk2bNk0RERFO31wOKC6UMAA4WLZsmV544QVt3bpVp0+fVoUKFdSpUycNHTr0mvuW2JIqMzNTSUlJ+uGHH3T06FH5+vqqRYsWeuWVV65oMidQEhBAAAAApzEHAgAAOI0AAgAAOI0AAgAAOI0AAgAAOK3ETKnO+fXr4h4CUOKUbciSPqAwp8/su6rHv3Dsr31FemE8Qyq77FglSYkJIAAAKDHy+KIwK5QwAACA08hAAABgZuQV9whKPAIIAADM8gggrBBAAABgYpCBsMQcCAAA4DQyEAAAmFHCsEQAAQCAGSUMS5QwAACA08hAAABgxo2kLBFAAABgRgnDEiUMAADgNDIQAACYsQrDEgEEAAAm3EjKGiUMAADgNDIQAACYUcKwRAABAIAZJQxLBBAAAJhxHwhLzIEAAABOIwMBAIAZJQxLBBAAAJgxidISJQwAAOA0MhAAAJhRwrBEAAEAgBklDEuUMAAAgNPIQAAAYGIY3AfCCgEEAABmzIGwRAkDAAA4jQwEAABmTKK0RAABAIAZJQxLBBAAAJjxZVqWmAMBAACcRgYCAAAzShiWCCAAADBjEqUlShgAAMBpZCAAADCjhGGJAAIAADNKGJYoYQAAAKeRgQAAwIwMhCUCCAAATPg2TmuUMAAAgNPIQAAAYEYJwxIBBAAAZizjtEQJAwAAs7w8121OWL16tVq3bq2oqCjZbDZ99tlnDvsNw9Dw4cMVGRkpHx8fxcfHa9euXQ59Tpw4oY4dOyogIEBBQUHq1q2bTp8+7dDn119/VdOmTeXt7a3y5ctr7NixTj9FBBAAAJQQ2dnZqlWrliZPnlzo/rFjx2rChAmaNm2a1q5dK19fXyUkJOjcuXP2Ph07dtSWLVu0bNkyLVy4UKtXr1ZSUpJ9f1ZWlu6++25VrFhRGzdu1Lhx4zRy5EhNnz7dqbHaDMMw/tplulbOr18X9xCAEqdsw6eKewhAiXT6zL6revyzy6e57Fg+8X/tfWyz2bRgwQK1adNG0p/Zh6ioKA0YMEADBw6UJGVmZio8PFwzZ85UYmKitm3bptjYWK1fv17169eXJC1ZskT33nuvDh06pKioKE2dOlVDhw5VamqqSpUqJUl67rnn9Nlnn2n79u1XPD4yEAAAmLmwhJGTk6OsrCyHLScnx+kh7du3T6mpqYqPj7e3BQYGqkGDBkpJSZEkpaSkKCgoyB48SFJ8fLzc3Ny0du1ae59mzZrZgwdJSkhI0I4dO3Ty5MkrHg8BBAAAV1FycrICAwMdtuTkZKePk5qaKkkKDw93aA8PD7fvS01NVVhYmMN+Dw8PBQcHO/Qp7BiXnuNKsAoDAAAzF67CGDJkiPr37+/Q5uXl5bLjFxcCCAAAzFx4HwgvLy+XBAwRERGSpLS0NEVGRtrb09LSVLt2bXuf9PR0h8ddvHhRJ06csD8+IiJCaWlpDn3yf87vcyUoYQAAcA2Ijo5WRESEVqxYYW/LysrS2rVrFRcXJ0mKi4tTRkaGNm7caO/zzTffKC8vTw0aNLD3Wb16tS5cuGDvs2zZMlWrVk1lypS54vEQQAAAYFZM94E4ffq0Nm3apE2bNkn6c+Lkpk2bdPDgQdlsNvXt21ejRo3SF198oc2bN+vxxx9XVFSUfaVG9erVdc8996hHjx5at26dfvjhB/Xu3VuJiYmKioqSJHXo0EGlSpVSt27dtGXLFs2bN09vvPFGgTKLFUoYAACYFdOdKDds2KDmzZvbf87/R71z586aOXOmBg8erOzsbCUlJSkjI0NNmjTRkiVL5O3tbX/MnDlz1Lt3b7Vo0UJubm5q166dJkyYYN8fGBiopUuXqlevXqpXr55CQkI0fPhwh3tFXAnuAwGUYNwHAijcVb8PxMLXXHYsn/uc+2R/rSADAQCAGV+mZYkAAgAAM75MyxIBBAAAZmQgLLEKAwAAOI0MBAAAZpQwLBFAAABgRgnDEiUMAADgNDIQAACYkYGwRAABAIBZybjHYolGCQMAADiNDAQAAGaUMCwRQAAAYEYAYYkSBgAAcBoZCAAAzLiRlCUCCAAAzChhWCKAAADAjGWclpgDAQAAnEYGAgAAM0oYlgggAAAwI4CwRAkDAAA4jQwEAABmLOO0RAABAICJkccqDCuUMAAAgNPIQAAAYMYkSksEEAAAmDEHwhIlDAAA4DQyEAAAmDGJ0hIBBAAAZsyBsEQAAQCAGQGEJeZAAAAAp5GBAADAjK/ztkQAcR3asHW3Zn6xQtv2/q6jJ7P0+qDuuvO2W+z7DcPQlHlf6ZMVKTqVfVa1Y6L1fI/2qhgZ5nCc1Ru3aNrHS7TrwBGVKuWh+rE36o3BPSRJn69cq2FT5hR6/pVvv6yygf5X7wIBF2nc+Db9X78k1alTQ5GR4Up8JEkLv1zm0Of5Yf3UpWuiAgMD9GPKBvX9v2Has2e/JKlChRv07JA+uv32RgoPD9Uff6Rp3oefaeyYybpw4UIxXBFchhKGJQKI69DZnPOqVvEGtW3eUP3++06B/TM+X665i1drVO+OuiGsrCZ9uEhPjZqqz8b/R16lPCVJy37cpBemfahnOtyn22rcpNzcXO3+/Q/7MRIa1VHj2tUdjvv85Nk6f+EiwQOuGaV9ffTb5m16/72P9MGHbxbY36//k3rq6S56Mmmg9u//XcOG99dnX8xS/bp3KSfnvG6qVkVubm56ps9Q7d2zX7E3V9OkSckqXbq0hv5ndDFcEfDPIYC4DjWtE6umdWIL3WcYhmYvWqUe7e5W81v/zEq83LuTmvcYqm/W/6qWjevpYm6uxsz4RP07PaAHW8TZH1ulfKT9v729Ssnbq5T95xOZp7Tut1164elHr9JVAa63bOkqLVu6qsj9vXo/obFjJmnRwj+zEkndB2jv/vVq3fpuffzxQi1ftlrLl62299+//3e9UbWyuvfoSABxrWMZpyUmUf7LHE4/rmMZWWpYs5q9zd/XRzVvrKhfduyXJG3be0jpJzLl5mZT+0FjdGeP5/X0y1O16+CRIo/75er18vEqpbsa1r7KVwD8MypVKq+IiDCtXPm9vS0r65Q2rN+k2xrULfJxgYH+Onky4x8YIa4qI89123XK6QzEsWPH9O677yolJUWpqamSpIiICDVq1EhdunRRaGioywcJ1zmWkSVJKhvkWGYoG+Sv4//bdyj9mCRp6keLNbBzW90QGqxZX65Ut5ET9eUbzyvQ37fAcResSFHLJvUcshLAtSw8/M+/Zen/ez/kS08/Zt9nVrlyRT351OMa+p/kqz4+oLg5lYFYv369brrpJk2YMEGBgYFq1qyZmjVrpsDAQE2YMEExMTHasGGD5XFycnKUlZXlsOWcP/+XLwKulfe/1F2PB+/WXQ1rK7ZKBb3Uq4Nskpb+uKlA/1927NPew2l68M6G/+xAgRIkMipcCz6fqQULFmvmjA+Lezj4u/IM123XKacyEH369NHDDz+sadOmyWazOewzDENPPfWU+vTpo5SUlMseJzk5WS+88IJD29CnOmrY052cGQ7+gpCgAEnS8YxTCi0TaG8/nnFK1SqVkySFlvmzT+VyEfb9pTw9dUN4iP44erLAMT9dkaKYSjcotkqFqzl04B+VlnZUkhQWFqK01KP29rCwEP3661aHvhGRYfpq8Qda++NP6tNryD86TlwdBqswLDmVgfjll1/Ur1+/AsGDJNlsNvXr10+bNm2yPM6QIUOUmZnpsA3u9ogzQ8FfdENYWYUEBWjtbzvtbafPnNXm3QdUq1olSVJs5fIq5emh/UfS7X0uXMzVkaMnFBVaxuF4Z87m6OuUn9X2zjgB15P9+39Xamq67rijsb3N399P9W+trXVrf7K3RUaFa/GSD7Xp58166slBMrh/AP4lnMpAREREaN26dYqJiSl0/7p16xQeHm55HC8vL3l5eTm05ZSidu4qZ87m6OAln5gOpx/X9n2HFOhXWpGhwXqs1e2a/snXqhARqhvCymryvEUKLROoO/+3KsOvtI8evquxpnz0lSJCghQZEqyZX6yQJN0dV8fhXEvW/KTc3Dy1alb/n7tAwEV8fUurcpWK9p8rViyvmrdU18kTmTp06IgmT3pXg5/trT179uvA/t/1/PD++uOPNH355VJJ+cHDB/r94GH95z+jFRIabD9WetqxAufDNeQ6Lj24ilMBxMCBA5WUlKSNGzeqRYsW9mAhLS1NK1as0FtvvaX//ve/V2WguHJb9h5Ut5ET7T+Pm7VAknT/7bdpVO/H1PWBeJ09d14vvvmhTp05qzoxlTV16NP2e0BIUv9ObeTu7q7/TJytnPPnVfPGSnp7RG8F+JV2ONeCb1LUosEtCvB1bAeuBXXr1tTir///fIUxY4dJkma//7GeenKQxr/2pnx9S2vipNEKDAxQypr1avtAF+Xk/Dln6847m+jGG6N1443R2rX7R4dj+5WO/ucuBK53Ha+ecBWb4WS+bd68eRo/frw2btyo3NxcSZK7u7vq1aun/v37q3379n9pIDm/fv2XHgdcz8o2fKq4hwCUSKfP7Luqx89+saPLjuU7vPC79l7rnF7G+cgjj+iRRx7RhQsXdOzYnym6kJAQeXp6WjwSAABcL/7ynSg9PT0VGRlp3REAgGsNqzAscStrAADMmERpiVtZAwAAp5GBAADAjFUYlgggAAAwo4RhiRIGAABwGhkIAABM+C4MawQQAACYUcKwRAkDAAA4jQwEAABmZCAsEUAAAGDGMk5LBBAAAJiRgbDEHAgAAOA0MhAAAJgYZCAsEUAAAGBGAGGJEgYAACVEbm6uhg0bpujoaPn4+KhKlSp66aWXZBj/P6AxDEPDhw9XZGSkfHx8FB8fr127djkc58SJE+rYsaMCAgIUFBSkbt266fTp0y4dKwEEAABmeXmu25wwZswYTZ06VZMmTdK2bds0ZswYjR07VhMnTrT3GTt2rCZMmKBp06Zp7dq18vX1VUJCgs6dO2fv07FjR23ZskXLli3TwoULtXr1aiUlJbns6ZEkm3FpWFOMcn79uriHAJQ4ZRs+VdxDAEqk02f2XdXjn+rZ0mXH8p+y+Ir73nfffQoPD9c777xjb2vXrp18fHw0e/ZsGYahqKgoDRgwQAMHDpQkZWZmKjw8XDNnzlRiYqK2bdum2NhYrV+/XvXr15ckLVmyRPfee68OHTqkqKgol1wXGQgAAK6inJwcZWVlOWw5OTmF9m3UqJFWrFihnTt3SpJ++eUXff/992rZ8s+AZt++fUpNTVV8fLz9MYGBgWrQoIFSUlIkSSkpKQoKCrIHD5IUHx8vNzc3rV271mXXRQABAIBZnuGyLTk5WYGBgQ5bcnJyoad97rnnlJiYqJiYGHl6eqpOnTrq27evOnbsKElKTU2VJIWHhzs8Ljw83L4vNTVVYWFhDvs9PDwUHBxs7+MKrMIAAMDEldX9IUOGqH///g5tXl5ehfb96KOPNGfOHM2dO1c333yzNm3apL59+yoqKkqdO3d22ZhcgQACAICryMvLq8iAwWzQoEH2LIQk1axZUwcOHFBycrI6d+6siIgISVJaWpoiIyPtj0tLS1Pt2rUlSREREUpPT3c47sWLF3XixAn7412BEgYAAGYuLGE448yZM3Jzc/yn2d3dXXn/W80RHR2tiIgIrVixwr4/KytLa9euVVxcnCQpLi5OGRkZ2rhxo73PN998o7y8PDVo0OCvPiMFkIEAAMCsmG4k1bp1a7388suqUKGCbr75Zv3888967bXX9MQTT0iSbDab+vbtq1GjRqlq1aqKjo7WsGHDFBUVpTZt2kiSqlevrnvuuUc9evTQtGnTdOHCBfXu3VuJiYkuW4EhEUAAAFBAcd3KeuLEiRo2bJh69uyp9PR0RUVF6cknn9Tw4cPtfQYPHqzs7GwlJSUpIyNDTZo00ZIlS+Tt7W3vM2fOHPXu3VstWrSQm5ub2rVrpwkTJrh0rNwHAijBuA8EULirfR+IzK7x1p2uUOCM5S47VklCBgIAADO+C8MSAQQAAGbO3YH6X4lVGAAAwGlkIAAAMCmuSZTXEgIIAADMCCAsUcIAAABOIwMBAIAZkygtEUAAAGDCHAhrlDAAAIDTyEAAAGBGCcMSAQQAACaUMKwRQAAAYEYGwhJzIAAAgNPIQAAAYGKQgbBEAAEAgBkBhCVKGAAAwGlkIAAAMKGEYY0AAgAAMwIIS5QwAACA08hAAABgQgnDGgEEAAAmBBDWCCAAADAhgLDGHAgAAOA0MhAAAJgZtuIeQYlHAAEAgAklDGuUMAAAgNPIQAAAYGLkUcKwQgABAIAJJQxrlDAAAIDTyEAAAGBisArDEgEEAAAmlDCsUcIAAABOIwMBAIAJqzCsEUAAAGBiGMU9gpKPAAIAABMyENaYAwEAAJxGBgIAABMyENYIIAAAMGEOhDVKGAAAwGlkIAAAMKGEYY0AAgAAE25lbY0SBgAAcBoZCAAATPguDGsEEAAAmORRwrBECQMAADiNDAQAACZMorRGAAEAgAnLOK0RQAAAYMKdKK0xBwIAADiNDAQAACaUMKwRQAAAYMIyTmuUMAAAgNPIQAAAYMIyTmsEEAAAmLAKwxolDAAA4DQyEAAAmDCJ0hoBBAAAJsyBsEYJAwAAOI0AAgAAE8Nw3easw4cP67HHHlPZsmXl4+OjmjVrasOGDZeMzdDw4cMVGRkpHx8fxcfHa9euXQ7HOHHihDp27KiAgAAFBQWpW7duOn369N99WhwQQAAAYJJn2Fy2OePkyZNq3LixPD09tXjxYm3dulWvvvqqypQpY+8zduxYTZgwQdOmTdPatWvl6+urhIQEnTt3zt6nY8eO2rJli5YtW6aFCxdq9erVSkpKctnzI0k2wygZi1U8St1Q3EMASpyzR74r7iEAJZJnSOWrevz1N7R12bFuPbzgivs+99xz+uGHH/Tdd4W/9w3DUFRUlAYMGKCBAwdKkjIzMxUeHq6ZM2cqMTFR27ZtU2xsrNavX6/69etLkpYsWaJ7771Xhw4dUlRU1N+/KJGBAACgxPjiiy9Uv359PfzwwwoLC1OdOnX01ltv2ffv27dPqampio+Pt7cFBgaqQYMGSklJkSSlpKQoKCjIHjxIUnx8vNzc3LR27VqXjZUAAgAAE1eWMHJycpSVleWw5eTkFHrevXv3aurUqapataq+/vprPf3003rmmWc0a9YsSVJqaqokKTw83OFx4eHh9n2pqakKCwtz2O/h4aHg4GB7H1cggAAAwMRw4ZacnKzAwECHLTk5udDz5uXlqW7duho9erTq1KmjpKQk9ejRQ9OmTbual/uXEEAAAHAVDRkyRJmZmQ7bkCFDCu0bGRmp2NhYh7bq1avr4MGDkqSIiAhJUlpamkOftLQ0+76IiAilp6c77L948aJOnDhh7+MKBBAAAJi4soTh5eWlgIAAh83Ly6vQ8zZu3Fg7duxwaNu5c6cqVqwoSYqOjlZERIRWrFhh35+VlaW1a9cqLi5OkhQXF6eMjAxt3LjR3uebb75RXl6eGjRo4LLniDtRAgBgUlx3ouzXr58aNWqk0aNHq3379lq3bp2mT5+u6dOnS5JsNpv69u2rUaNGqWrVqoqOjtawYcMUFRWlNm3aSPozY3HPPffYSx8XLlxQ7969lZiY6LIVGBIBBAAAJcatt96qBQsWaMiQIXrxxRcVHR2t119/XR07drT3GTx4sLKzs5WUlKSMjAw1adJES5Yskbe3t73PnDlz1Lt3b7Vo0UJubm5q166dJkyY4NKxch8IoATjPhBA4a72fSC+i3jIZcdqmvqxy45VkpCBAADAxBBfpmWFSZQAAMBpZCAAADDJKxHF/ZKNAAIAAJM8ShiWCCAAADBhDoQ15kAAAACnkYEAAMAkr7gHcA0ggAAAwIQShjVKGAAAwGlkIAAAMKGEYY0AAgAAEwIIa5QwAACA08hAAABgwiRKawQQAACY5BE/WKKEAQAAnEYGAgAAE74LwxoBBAAAJnwZpzUCCAAATFjGaY05EAAAwGlkIAAAMMmzMQfCCgEEAAAmzIGwRgkDAAA4jQwEAAAmTKK0RgABAIAJd6K0RgkDAAA4jQwEAAAm3InSGgEEAAAmrMKwRgkDAAA4jQwEAAAmTKK0RgABAIAJyzitEUAAAGDCHAhrzIEAAABOIwMBAIAJcyCsEUAAAGDCHAhrlDAAAIDTyEAAAGBCBsIaAQQAACYGcyAsUcIAAABOIwMBAIAJJQxrBBAAAJgQQFijhAEAAJxGBgIAABNuZW2NAAIAABPuRGmNAAIAABPmQFhjDgQAAHAaGQgAAEzIQFgjgAAAwIRJlNYoYQAAAKeRgQAAwIRVGNYIIAAAMGEOhDVKGAAAwGlkIAAAMGESpTUCCAAATPIIISxRwgAAAE4jAwEAgAmTKK0RQAAAYEIBwxoBBAAAJmQgrDEHAgAAOI0AAgAAkzyb67a/6pVXXpHNZlPfvn3tbefOnVOvXr1UtmxZ+fn5qV27dkpLS3N43MGDB9WqVSuVLl1aYWFhGjRokC5evPjXB1IEAggAAEzyZLhs+yvWr1+vN998U7fccotDe79+/fTll19q/vz5WrVqlY4cOaIHH3zQvj83N1etWrXS+fPntWbNGs2aNUszZ87U8OHD/9bzURgCCAAASpDTp0+rY8eOeuutt1SmTBl7e2Zmpt555x299tpruvPOO1WvXj3NmDFDa9as0Y8//ihJWrp0qbZu3arZs2erdu3aatmypV566SVNnjxZ58+fd+k4CSAAADAxXLg5q1evXmrVqpXi4+Md2jdu3KgLFy44tMfExKhChQpKSUmRJKWkpKhmzZoKDw+390lISFBWVpa2bNnyF0ZTNFZhAABg4spVGDk5OcrJyXFo8/LykpeXV4G+H374oX766SetX7++wL7U1FSVKlVKQUFBDu3h4eFKTU2197k0eMjfn7/PlchAAABwFSUnJyswMNBhS05OLtDv999/1//93/9pzpw58vb2LoaROocAAgAAE1dOohwyZIgyMzMdtiFDhhQ458aNG5Wenq66devKw8NDHh4eWrVqlSZMmCAPDw+Fh4fr/PnzysjIcHhcWlqaIiIiJEkREREFVmXk/5zfx1UIIAAAMHHlHAgvLy8FBAQ4bIWVL1q0aKHNmzdr06ZN9q1+/frq2LGj/b89PT21YsUK+2N27NihgwcPKi4uTpIUFxenzZs3Kz093d5n2bJlCggIUGxsrEufI+ZAAABQAvj7+6tGjRoObb6+vipbtqy9vVu3burfv7+Cg4MVEBCgPn36KC4uTg0bNpQk3X333YqNjVWnTp00duxYpaam6vnnn1evXr0KDVr+DgIIAABMSuqtrMePHy83Nze1a9dOOTk5SkhI0JQpU+z73d3dtXDhQj399NOKi4uTr6+vOnfurBdffNHlY7EZhlEivjPEo9QNxT0EoMQ5e+S74h4CUCJ5hlS+qsfvXynRZcd6bf+HLjtWSUIGAgAAkxLxybqEYxIlAABwGhkIAABMSuociJKEAAIAABODIoYlShgAAMBpZCAAADChhGGNAAIAAJM8ShiWKGEAAACnkYEAAMCE/IM1MhD/Qrt3/qiL5w8X2Ca88bLKlAnS6+Nf0pbfVutU5m7t3b1O4197UQEB/sU9bOBv2bBps3oNHqHm93dUjcYttWL1Gof9y779QT36/keNW7ZXjcYttX3nniKPZRiGnhowrNDj/JGarqcHDlf9O9uoWatE/XfS27p4MfeqXBOuHld+G+f1igDiX6hho3t1Q/na9i3hnj9v2frJJwsVFRWuqKhwPfvsS6pVp4W6de+nhITmemv6q8U8auDvOXv2nKrdWFlDB/QsfP+5c6p7y83q9/QTlsd6f95nshXSnpubq56DRujChYuaPe1Vvfz8AH2+eJkmvf3+3xw9UPJQwvgXOnbshMPPgwf11u7d+7RqdYokqf0jSfZ9e/ce0LDhY/TezAlyd3dXbi6fpHBtahp3q5rG3Vrk/vvvaSFJOvxH2mWPs33nHs368BPNe2eC7ri/o8O+Net+0p79B/XWG6MVElxGMaqi3t0f1/ip76pXt47y9PT8+xeCfwSrMKyRgfiX8/T0VMcOD2rmrHlF9gkM8FdW1mmCB/zrnT13ToNfGKOhA3oppGxwgf2//LZNVStXUkhwGXtb4wb1dDr7jHbvO/BPDhV/k+HC/12vXB5A/P7773riCesUIEqGBx64R0FBAZr13keF7i9btoyG/qev3n5nzj88MqDkGTthumrXiNWdTeMK3X/sxEmVDQ5yaMv/+djxk1d5dHClPBdu1yuXBxAnTpzQrFmzLtsnJydHWVlZDlsJ+Vbxf50nuiRqydcr9UchaVt/fz99+fl72rZtp154kTkQ+Hdb+d2PWrvxFz33f08W91CAEsHpORBffPHFZffv3bvX8hjJycl64YUXHNpsbn6yuQc4Oxz8DRUq3KAWLZrqofbdC+zz8/PVVwvn6NSpbLV7uLsuXrxYDCMESo61Gzfp98N/KO6ehxza+w19WXVr3ayZk8YqJLiMNm/d6bD/+IkMSVJI2TLCteN6Lj24itMBRJs2bWSz2S6bMbDZCpuf/P8NGTJE/fv3d2grUzbG2aHgb+rS+RGlpx/TV1+tcGj39/fT4kVzlZOTozYPdlFOTk4xjRAoObp3aq9299/j0Na209Ma/EyS7mjcQJJUq0Z1TX9vno6fzFDZMkGSpJT1P8nPt7SqVKrwTw8Zf8P1XHpwFacDiMjISE2ZMkUPPPBAofs3bdqkevXqXfYYXl5e8vLycmizCjrgWjabTZ0ff0Tvz57vMDnS399PS776QD6lvfV4lz4KCPC33wPi6NHjysvjbYVr05kzZ3Xw0BH7z4ePpGn7zj0KDPBXZESYMrNO6Y/UdKUfOy5J2nfwkKQ/MwchZYPtm1lkeKjKRUVIkhrdVldVKlXQkBfHqX/Pbjp+4qQmTn9PiQ+2VqlSpf6BqwT+OU4HEPXq1dPGjRuLDCCsshMoGeJbNFXFiuU0Y6bj6ou6dWqqQYO6kqSd2x1vkFOlagMdOHDoHxsj4Eq/bd+lJ/o8a/957MTpkqQHWsbr5ecHaOV3P+r50a/Z9w8a8Yok6eknOqpXt8eu6Bzu7u6aPG6kXho3SY892V8+Pl66v2W8enfv5MIrwT8hj3/HLNkMJ/+1/+6775Sdna177rmn0P3Z2dnasGGDbr/9dqcG4lHqBqf6A/8GZ498V9xDAEokz5DKV/X4j1V80GXHmn3gU5cdqyRxOgPRtGnTy+739fV1OngAAADXFu5ECQCAyfX8HRauQgABAIAJyzitcStrAADgNDIQAACYsGDdGgEEAAAmzIGwRgABAIAJcyCsMQcCAAA4jQwEAAAmzIGwRgABAIAJX8lgjRIGAABwGhkIAABMWIVhjQACAAAT5kBYo4QBAACcRgYCAAAT7gNhjQACAAAT5kBYo4QBAACcRgYCAAAT7gNhjQACAAATVmFYI4AAAMCESZTWmAMBAACcRgYCAAATVmFYI4AAAMCESZTWKGEAAACnkYEAAMCEEoY1AggAAExYhWGNEgYAAHAaGQgAAEzymERpiQACAAATwgdrlDAAAIDTyEAAAGDCKgxrBBAAAJgQQFgjgAAAwIQ7UVpjDgQAAHAaGQgAAEwoYVgjgAAAwIQ7UVqjhAEAAJxGBgIAABMmUVojgAAAwIQ5ENYoYQAAUEIkJyfr1ltvlb+/v8LCwtSmTRvt2LHDoc+5c+fUq1cvlS1bVn5+fmrXrp3S0tIc+hw8eFCtWrVS6dKlFRYWpkGDBunixYsuHSsBBAAAJoZhuGxzxqpVq9SrVy/9+OOPWrZsmS5cuKC7775b2dnZ9j79+vXTl19+qfnz52vVqlU6cuSIHnzwQfv+3NxctWrVSufPn9eaNWs0a9YszZw5U8OHD3fZ8yNJNqOEFHo8St1Q3EMASpyzR74r7iEAJZJnSOWrevxaEY1cdqxfUtf85ccePXpUYWFhWrVqlZo1a6bMzEyFhoZq7ty5euihhyRJ27dvV/Xq1ZWSkqKGDRtq8eLFuu+++3TkyBGFh4dLkqZNm6Znn31WR48eValSpVxyXWQgAAC4inJycpSVleWw5eTkXNFjMzMzJUnBwcGSpI0bN+rChQuKj4+394mJiVGFChWUkpIiSUpJSVHNmjXtwYMkJSQkKCsrS1u2bHHVZRFAAABgZrjwf8nJyQoMDHTYkpOTLceQl5envn37qnHjxqpRo4YkKTU1VaVKlVJQUJBD3/DwcKWmptr7XBo85O/P3+cqrMIAAMAkz4XV/SFDhqh///4ObV5eXpaP69Wrl3777Td9//33LhuLKxFAAABg4so7UXp5eV1RwHCp3r17a+HChVq9erXKlStnb4+IiND58+eVkZHhkIVIS0tTRESEvc+6descjpe/SiO/jytQwgAAoIQwDEO9e/fWggUL9M033yg6Otphf7169eTp6akVK1bY23bs2KGDBw8qLi5OkhQXF6fNmzcrPT3d3mfZsmUKCAhQbGysy8ZKBgIAABNXljCc0atXL82dO1eff/65/P397XMWAgMD5ePjo8DAQHXr1k39+/dXcHCwAgIC1KdPH8XFxalhw4aSpLvvvluxsbHq1KmTxo4dq9TUVD3//PPq1auX05mQy2EZJ1CCsYwTKNzVXsYZE3ary461PX39Ffe12WyFts+YMUNdunSR9OeNpAYMGKAPPvhAOTk5SkhI0JQpUxzKEwcOHNDTTz+tb7/9Vr6+vurcubNeeeUVeXi4Lm9AAAGUYAQQQOGu1wDiWkIJAwAAk+IqYVxLCCAAADBx5SqM6xWrMAAAgNPIQAAAYEIJwxoBBAAAJpQwrFHCAAAATiMDAQCAiWHkFfcQSjwCCAAATPIoYVgigAAAwKSE3GOxRGMOBAAAcBoZCAAATChhWCOAAADAhBKGNUoYAADAaWQgAAAw4U6U1gggAAAw4U6U1ihhAAAAp5GBAADAhEmU1gggAAAwYRmnNUoYAADAaWQgAAAwoYRhjQACAAATlnFaI4AAAMCEDIQ15kAAAACnkYEAAMCEVRjWCCAAADChhGGNEgYAAHAaGQgAAExYhWGNAAIAABO+TMsaJQwAAOA0MhAAAJhQwrBGAAEAgAmrMKxRwgAAAE4jAwEAgAmTKK0RQAAAYEIJwxoBBAAAJgQQ1pgDAQAAnEYGAgAAE/IP1mwGeRpcIicnR8nJyRoyZIi8vLyKezhAicD7AiiIAAIOsrKyFBgYqMzMTAUEBBT3cIASgfcFUBBzIAAAgNMIIAAAgNMIIAAAgNMIIODAy8tLI0aMYKIYcAneF0BBTKIEAABOIwMBAACcRgABAACcRgABAACcRgABAACcRgABu8mTJ6tSpUry9vZWgwYNtG7duuIeElCsVq9erdatWysqKko2m02fffZZcQ8JKDEIICBJmjdvnvr3768RI0bop59+Uq1atZSQkKD09PTiHhpQbLKzs1WrVi1Nnjy5uIcClDgs44QkqUGDBrr11ls1adIkSVJeXp7Kly+vPn366Lnnnivm0QHFz2azacGCBWrTpk1xDwUoEchAQOfPn9fGjRsVHx9vb3Nzc1N8fLxSUlKKcWQAgJKKAAI6duyYcnNzFR4e7tAeHh6u1NTUYhoVAKAkI4AAAABOI4CAQkJC5O7urrS0NIf2tLQ0RUREFNOoAAAlGQEEVKpUKdWrV08rVqywt+Xl5WnFihWKi4srxpEBAEoqj+IeAEqG/v37q3Pnzqpfv75uu+02vf7668rOzlbXrl2Le2hAsTl9+rR2795t/3nfvn3atGmTgoODVaFChWIcGVD8WMYJu0mTJmncuHFKTU1V7dq1NWHCBDVo0KC4hwUUm2+//VbNmzcv0N65c2fNnDnznx8QUIIQQAAAAKcxBwIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADiNAAIAADjt/wG4CZETjMtRPgAAAABJRU5ErkJggg==\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Treinando Random Forest...\n", "<PERSON><PERSON><PERSON><PERSON><PERSON> média (Random Forest - validação cruzada): 0.9240\n", "\n", "Avaliação do modelo Random Forest:\n", "Acurácia: 0.9181\n", "Precisão: 0.9213\n", "Recall: 0.9175\n", "F1-Score: 0.9194\n", "AUC-ROC: 0.9744\n", "<PERSON><PERSON>:\n", "[[1074   95]\n", " [ 100 1112]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAhAAAAGzCAYAAAB+YC5UAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAPjZJREFUeJzt3XlcVdX+//H3QRAQBERkMucsM00NS3E2MRyynPJrUqKZehM0JTO9Od0yKW1wyPHWVW9qpTZY3jRNS8vIWTOnzBxSA5yARAWB/fvDH+d6Nuj25CHI+3r22I9HZ+21915nM/jh81lrH5thGIYAAACc4FbcAwAAAH89BBAAAMBpBBAAAMBpBBAAAMBpBBAAAMBpBBAAAMBpBBAAAMBpBBAAAMBpBBAAAMBpBBC3uPHjx8tmsxXpNWw2m8aPH1+k1/izTZ48WdWrV1epUqVUv379IrnG8OHDVbZsWcXGxurs2bOqXbu2du7cWSTX+qv6+uuvZbPZ9PXXXxf3UACYEEC4yPz582Wz2WSz2fTtt98W2G8YhipVqiSbzaaHHnroD11j4sSJ+uSTT25ypH8Nubm5mjdvnlq1aqXAwEB5enqqatWq6tu3r7Zu3Vqk1169erVGjBihpk2bat68eZo4caLLr3H+/HnNmjVLL774ovbs2aOgoCD5+vrqnnvucfm1blT+92/+5ufnp5YtW+o///lPsY2pJDLfp/wtNDS0uIdWqM8///yWC/BRMrgX9wBuNV5eXlq8eLGaNWvm0L5+/XodP35cnp6ef/jcEydOVPfu3dW5c+cbPmb06NEaOXLkH75mcbh48aK6du2qVatWqUWLFvr73/+uwMBAHTlyREuWLNGCBQt07Ngx3XbbbUVy/XXr1snNzU3vvPOOSpcuXSTX8PLy0t69e1WlShUNGzZMJ0+eVGhoqNzcijemb9u2rXr37i3DMHT06FHNmjVLnTp10sqVKxUdHV2sYytJ8u/T1by9vYtpNNf3+eefa8aMGQQRcDkCCBfr0KGDli5dqmnTpsnd/b+3d/HixYqIiNDp06f/lHFkZmbKx8dH7u7uDuP4K3juuee0atUqvfnmmxo6dKjDvnHjxunNN98s0uunpqbK29u7yIIHSXJ3d1eVKlXsr8PDw4vsWs6444479Pjjj9tfd+vWTbVr19bUqVMJIK5ivk+ukpOTo7y8vCL93gNchRKGiz322GM6c+aM1qxZY2/Lzs7WsmXL1KtXr0KPee2119SkSROVL19e3t7eioiI0LJlyxz62Gw2ZWZmasGCBfaUaZ8+fST9d57D3r171atXL5UrV86eATHPgejTp881U7BWf6FkZWVp2LBhqlChgsqWLauHH35Yx48fL7TviRMn9OSTTyokJESenp66++679a9//cvq9un48eOaM2eO2rZtWyB4kKRSpUpp+PDhDtmHHTt2qH379vLz85Ovr6/atGmj77//3uG4/BLTxo0blZCQoAoVKsjHx0ddunTRqVOn7P1sNpvmzZunzMxM+32ZP3++jhw5Yv9/M/O9+/333zV06FBVrVpVnp6eCg4OVtu2bbV9+3Z7n6+//lrdu3dX5cqV5enpqUqVKmnYsGG6ePFigfOvW7dOzZs3l4+PjwICAvTII49o3759lvfSFe666y4FBQXp0KFDDu3Lly9Xx44dFR4eLk9PT9WoUUMvvfSScnNzHfq1atVKderU0d69e9W6dWuVKVNGFStW1KRJkwpc6/jx4+rcubN8fHwUHBysYcOGKSsrq9BxLV26VBEREfL29lZQUJAef/xxnThxwqFPnz595Ovrq2PHjumhhx6Sr6+vKlasqBkzZkiSdu/erQceeEA+Pj6qUqWKFi9efDO3ykFqaqr69eunkJAQeXl5qV69elqwYIFDn/zvqddee01TpkxRjRo15Onpqb1790qS9u/fr+7duyswMFBeXl5q2LChPv30U4dzXL58Wf/4xz9Us2ZNeXl5qXz58mrWrJn990+fPn3s7/fqn3XAFf5af5r+BVStWlWRkZF677331L59e0nSypUrlZ6erp49e2ratGkFjpk6daoefvhhxcTEKDs7W++//74effRRrVixQh07dpQkvfvuu3rqqad0//33a8CAAZKkGjVqOJzn0UcfVc2aNTVx4kRd61PaBw4cqKioKIe2VatWadGiRQoODr7ue3vqqae0cOFC9erVS02aNNG6devs47taSkqKGjduLJvNpvj4eFWoUEErV65Uv379lJGRUWhgkG/lypXKycnRE088cd2x5NuzZ4+aN28uPz8/jRgxQh4eHpozZ45atWql9evXq1GjRg79Bw8erHLlymncuHE6cuSIpkyZovj4eH3wwQeSrtznuXPnavPmzXr77bclSU2aNLmhseT729/+pmXLlik+Pl61a9fWmTNn9O2332rfvn269957JUlLlizRxYsXNWjQIAUGBmrz5s2aPn26jh8/rqVLl9rP9eWXX6p9+/aqXr26xo8fr4sXL2r69Olq2rSptm/frqpVqzo1Nmelp6fr3LlzBb7X5s+fL19fXyUkJMjX11fr1q3T2LFjlZGRocmTJzv0PXfunNq1a6euXbuqR48eWrZsmZ5//nnVrVvX/jNy8eJFtWnTRseOHdOQIUMUHh6ud999V+vWrSswpvnz56tv37667777lJiYqJSUFE2dOlUbN27Ujh07FBAQYO+bm5ur9u3bq0WLFpo0aZIWLVqk+Ph4+fj46IUXXlBMTIy6du2q2bNnq3fv3oqMjFS1atUs78ulS5cKZBPLli0rT09PXbx4Ua1atdLPP/+s+Ph4VatWTUuXLlWfPn2UlpamZ555xuG4efPm6dKlSxowYIA8PT0VGBioPXv2qGnTpqpYsaJGjhwpHx8fLVmyRJ07d9aHH36oLl26SLryB0JiYqL9d0NGRoa2bt2q7du3q23btho4cKBOnjypNWvW6N1337V8X4BTDLjEvHnzDEnGli1bjLfeessoW7asceHCBcMwDOPRRx81WrdubRiGYVSpUsXo2LGjw7H5/fJlZ2cbderUMR544AGHdh8fHyM2NrbAtceNG2dIMh577LFr7ruWgwcPGv7+/kbbtm2NnJyca/bbuXOnIckYNGiQQ3uvXr0MSca4cePsbf369TPCwsKM06dPO/Tt2bOn4e/vX+D9Xm3YsGGGJGPHjh3X7HO1zp07G6VLlzYOHTpkbzt58qRRtmxZo0WLFva2/K9PVFSUkZeX53C9UqVKGWlpafa22NhYw8fHx+E6hw8fNiQZ8+bNKzAG8/v39/c34uLirjvuzMzMAm2JiYmGzWYzjh49am+rX7++ERwcbJw5c8betmvXLsPNzc3o3bv3da/hLElGv379jFOnThmpqanG1q1bjXbt2hmSjMmTJzv0LexrOHDgQKNMmTLGpUuX7G0tW7Y0JBn//ve/7W1ZWVlGaGio0a1bN3vblClTDEnGkiVL7G2ZmZnG7bffbkgyvvrqK8MwrvxsBAcHG3Xq1DEuXrxo77tixQpDkjF27Fh7W2xsrCHJmDhxor3t3Llzhre3t2Gz2Yz333/f3r5///4CX8fr3afCtvzvjfz3snDhQvsx2dnZRmRkpOHr62tkZGQYhvHf7yk/Pz8jNTXV4Rpt2rQx6tat63Av8/LyjCZNmhg1a9a0t9WrV6/A7xOzuLi46/4OAP4oShhFoEePHrp48aJWrFih33//XStWrLhm+UJynHx17tw5paenq3nz5g4p7xvxt7/9zan+mZmZ6tKli8qVK6f33ntPpUqVumbfzz//XJI0ZMgQh3ZzNsEwDH344Yfq1KmTDMPQ6dOn7Vt0dLTS09Ov+74yMjIkXflrzkpubq5Wr16tzp07q3r16vb2sLAw9erVS99++639fPkGDBjgkMJt3ry5cnNzdfToUcvr3aiAgABt2rRJJ0+evGafMmXK2P8/MzNTp0+fVpMmTWQYhnbs2CFJ+u2337Rz50716dNHgYGB9v733HOP2rZta/+auNI777yjChUqKDg4WA0bNtTatWs1YsQIJSQkOPS7+nv2999/1+nTp9W8eXNduHBB+/fvd+jr6+vrMF+gdOnSuv/++/XLL7/Y2z7//HOFhYWpe/fu9rYyZcrYs235tm7dqtTUVA0aNEheXl729o4dO6pWrVqFrhh56qmn7P8fEBCgO++8Uz4+PurRo4e9/c4771RAQIDDmK7nkUce0Zo1axy2/Dkin3/+uUJDQ/XYY4/Z+3t4eGjIkCE6f/681q9f73Cubt26qUKFCvbXZ8+e1bp169SjRw/7vT19+rTOnDmj6OhoHTx40F6uCQgI0J49e3Tw4MEbGjfgSpQwikCFChUUFRWlxYsX68KFC8rNzXX4xWi2YsUKTZgwQTt37nSo+Tpbq7yR1OvV+vfvr0OHDum7775T+fLlr9v36NGjcnNzK5DKvvPOOx1enzp1SmlpaZo7d67mzp1b6LlSU1OveR0/Pz9JV/5RsnLq1ClduHChwBikK7X7vLw8/frrr7r77rvt7ZUrV3boV65cOUlXAjdXmTRpkmJjY1WpUiVFRESoQ4cO6t27t0OQc+zYMY0dO1affvppgWunp6dLkj2oudb7++KLL+yTZQuTnJzs8Nrf399ypcAjjzyi+Ph4ZWdna8uWLZo4caIuXLhQYHXInj17NHr0aK1bt65AkJY//ny33XZbge/lcuXK6YcffrC/Pnr0qG6//fYC/czv/Xr3pFatWgWWUHt5eTn84yxduQ+Fjcnf3/+Gvw9uu+22AqXAq8dYs2bNAvfsrrvucngP+cw/tz///LMMw9CYMWM0ZsyYQq+RmpqqihUr6sUXX9QjjzyiO+64Q3Xq1FG7du30xBNPFOtyYPzvIIAoIr169VL//v2VnJys9u3bO9Rlr/bNN9/o4YcfVosWLTRz5kyFhYXJw8ND8+bNc3pSlzPLyKZOnar33ntPCxcudOmDkvLy8iRJjz/+uGJjYwvtc71fbrVq1ZJ0ZYJbUTzA6VpZFuMac0byXSuYM08alK5koJo3b66PP/5Yq1ev1uTJk/Xqq6/qo48+Uvv27ZWbm6u2bdvq7Nmzev7551WrVi35+PjoxIkT6tOnj/0e3qywsDCH1/PmzbNPvL2Wq/9h7NChg4KCghQfH6/WrVura9eukqS0tDS1bNlSfn5+evHFF1WjRg15eXlp+/btev755wuM/4/ec1e41rWLc0xm5p/b/Ps3fPjwa658uf322yVJLVq00KFDh7R8+XKtXr1ab7/9tt58803Nnj3bIfMCFAUCiCLSpUsXDRw4UN9//719gl5hPvzwQ3l5eemLL75weEbEvHnzCvR11ezpb775RsOHD9fQoUMVExNzQ8dUqVJFeXl5OnTokMNffwcOHHDol79CIzc395p/oV1P+/btVapUKS1cuNByImWFChVUpkyZAmOQrsxgd3NzU6VKlZweQ2HyMxVpaWkO7dcqfYSFhWnQoEEaNGiQUlNTde+99+rll19W+/bttXv3bv30009asGCBw7MErl65I8m+zPNa7y8oKOia2YfCznd1JuZGDRw4UG+++aZGjx6tLl262J8KeebMGX300Udq0aKFve/hw4edPn++KlWq6Mcff5RhGA7f5+b3fvU9eeCBBxz2HThwwGFpbHGpUqWKfvjhB+Xl5TlkIfJLO1ZjzM9UeXh43NDPUGBgoPr27au+ffvq/PnzatGihcaPH28PIFh1gaLCHIgi4uvrq1mzZmn8+PHq1KnTNfuVKlVKNpvN4S/ZI0eOFPrESR8fnwL/gDnrt99+U48ePdSsWbMCs+WvJ3+2vHkVyZQpUxxelypVSt26ddOHH36oH3/8scB5rl4yWZhKlSqpf//+Wr16taZPn15gf15enl5//XUdP35cpUqV0oMPPqjly5fryJEj9j4pKSn2h3nll0Rulp+fn4KCgrRhwwaH9pkzZzq8zs3NLZDCDw4OVnh4uL08lf/X79V/7RqGoalTpzocFxYWpvr162vBggUOX/cff/xRq1evVocOHa475qioKIfNnJG4Ee7u7nr22We1b98+LV++/Jrjz87OLnAvnNGhQwedPHnSYfnyhQsXCpTBGjZsqODgYM2ePduh3Ldy5Urt27ev0FVBf7YOHTooOTnZ4Q+HnJwcTZ8+Xb6+vmrZsuV1jw8ODlarVq00Z84c/fbbbwX2X/0zdObMGYd9vr6+uv322x3uTX6QebO/OwAzMhBF6Fop/Kt17NhRb7zxhtq1a6devXopNTVVM2bM0O233+5QI5akiIgIffnll3rjjTcUHh6uatWqFVimaGXIkCE6deqURowYoffff99h3z333HPN8kL9+vX12GOPaebMmUpPT1eTJk20du1a/fzzzwX6vvLKK/rqq6/UqFEj9e/fX7Vr19bZs2e1fft2ffnllzp79ux1x/j666/r0KFDGjJkiD766CM99NBDKleunI4dO6alS5dq//796tmzpyRpwoQJWrNmjZo1a6ZBgwbJ3d1dc+bMUVZWVqHPGrgZTz31lF555RU99dRTatiwoTZs2KCffvrJoc/vv/+u2267Td27d1e9evXk6+urL7/8Ulu2bNHrr78u6UqZpkaNGho+fLhOnDghPz8/ffjhh4XW3ydPnqz27dsrMjJS/fr1sy/j9Pf3/9OeLNinTx+NHTtWr776qjp37qwmTZqoXLlyio2N1ZAhQ2Sz2fTuu+/eVPq/f//+euutt9S7d29t27ZNYWFhevfddx0mm0pX/ip/9dVX1bdvX7Vs2VKPPfaYfRln1apVNWzYsJt9uzdtwIABmjNnjvr06aNt27apatWqWrZsmTZu3KgpU6bc0AThGTNmqFmzZqpbt6769++v6tWrKyUlRUlJSTp+/Lh27dolSapdu7ZatWqliIgIBQYGauvWrfYlxPkiIiIkXfnZj46OVqlSpew/P8BNKZ7FH7eeq5dxXk9hyzjfeecdo2bNmoanp6dRq1YtY968eYUuv9y/f7/RokULw9vb25BkX9KZ3/fUqVMFrmc+T/6yusI2qyVsFy9eNIYMGWKUL1/e8PHxMTp16mT8+uuvhR6bkpJixMXFGZUqVTI8PDyM0NBQo02bNsbcuXOve418OTk5xttvv200b97c8Pf3Nzw8PIwqVaoYffv2LbDEc/v27UZ0dLTh6+trlClTxmjdurXx3XffOfS51tfnq6++clgmaBiFL+M0jCtLF/v162f4+/sbZcuWNXr06GGkpqY6vP+srCzjueeeM+rVq2eULVvW8PHxMerVq2fMnDnT4Vx79+41oqKiDF9fXyMoKMjo37+/sWvXrkKXin755ZdG06ZNDW9vb8PPz8/o1KmTsXfv3hu6j86QdM3lp+PHj3e4Txs3bjQaN25seHt7G+Hh4caIESOML774osC9bNmypXH33XcXOF9sbKxRpUoVh7ajR48aDz/8sFGmTBkjKCjIeOaZZ4xVq1YVOKdhGMYHH3xgNGjQwPD09DQCAwONmJgY4/jx4wWuUdjX8VpjKuxnszDXu0/5UlJSjL59+xpBQUFG6dKljbp16xb4uuYv4zQvkc136NAho3fv3kZoaKjh4eFhVKxY0XjooYeMZcuW2ftMmDDBuP/++42AgADD29vbqFWrlvHyyy8b2dnZ9j45OTnG4MGDjQoVKhg2m40lnXAZm2EUw6whAADwl8YcCAAA4DQCCAAA4DQCCAAA4DQCCAAA4DQCCAAA4DQCCAAA4DQCCAAA4LQS8yTK7F82F/cQgBKnTK0uxT0EoETKyT5RpOe/fPrGPtr9RngEVbfu9BdUYgIIAABKjLyCn7QLR5QwAACA08hAAABgZuQV9whKPAIIAADM8gggrBBAAABgYpCBsMQcCAAA4DQyEAAAmFHCsEQAAQCAGSUMS5QwAACA08hAAABgxoOkLBFAAABgRgnDEiUMAADgNDIQAACYsQrDEgEEAAAmPEjKGiUMAADgNDIQAACYUcKwRAABAIAZJQxLBBAAAJjxHAhLzIEAAABOIwMBAIAZJQxLBBAAAJgxidISJQwAAOA0MhAAAJhRwrBEAAEAgBklDEuUMAAAgNPIQAAAYGIYPAfCCgEEAABmzIGwRAkDAAA4jQwEAABmTKK0RAABAIAZJQxLBBAAAJjxYVqWmAMBAACcRgYCAAAzShiWCCAAADBjEqUlShgAAMBpZCAAADCjhGGJDAQAAGZ5ea7bnLBhwwZ16tRJ4eHhstls+uSTTxz2G4ahsWPHKiwsTN7e3oqKitLBgwcd+pw9e1YxMTHy8/NTQECA+vXrp/Pnzzv0+eGHH9S8eXN5eXmpUqVKmjRpktO3iAACAIASIjMzU/Xq1dOMGTMK3T9p0iRNmzZNs2fP1qZNm+Tj46Po6GhdunTJ3icmJkZ79uzRmjVrtGLFCm3YsEEDBgyw78/IyNCDDz6oKlWqaNu2bZo8ebLGjx+vuXPnOjVWm2EYxh97m66V/cvm4h4CUOKUqdWluIcAlEg52SeK9PyXvnnXZeey3d9DWVlZDm2enp7y9PS8/nE2mz7++GN17txZ0pXsQ3h4uJ599lkNHz5ckpSenq6QkBDNnz9fPXv21L59+1S7dm1t2bJFDRs2lCStWrVKHTp00PHjxxUeHq5Zs2bphRdeUHJyskqXLi1JGjlypD755BPt37//ht8XGQgAAEwMI9dlW2Jiovz9/R22xMREp8d0+PBhJScnKyoqyt7m7++vRo0aKSkpSZKUlJSkgIAAe/AgSVFRUXJzc9OmTZvsfVq0aGEPHiQpOjpaBw4c0Llz5254PEyiBACgCI0aNUoJCQkObVbZh8IkJydLkkJCQhzaQ0JC7PuSk5MVHBzssN/d3V2BgYEOfapVq1bgHPn7ypUrd0PjIYAAAMDMhc+BuJFyxV8RJQwAAMyMPNdtLhIaGipJSklJcWhPSUmx7wsNDVVqaqrD/pycHJ09e9ahT2HnuPoaN4IAAgAAs2Jaxnk91apVU2hoqNauXWtvy8jI0KZNmxQZGSlJioyMVFpamrZt22bvs27dOuXl5alRo0b2Phs2bNDly5ftfdasWaM777zzhssXEgEEAAAlxvnz57Vz507t3LlT0pWJkzt37tSxY8dks9k0dOhQTZgwQZ9++ql2796t3r17Kzw83L5S46677lK7du3Uv39/bd68WRs3blR8fLx69uyp8PBwSVKvXr1UunRp9evXT3v27NEHH3ygqVOnFpinYYU5EAAAmBXTkyi3bt2q1q1b21/n/6MeGxur+fPna8SIEcrMzNSAAQOUlpamZs2aadWqVfLy8rIfs2jRIsXHx6tNmzZyc3NTt27dNG3aNPt+f39/rV69WnFxcYqIiFBQUJDGjh3r8KyIG8FzIIASjOdAAIUr6udAXFw902Xn8n5wkMvOVZJQwgAAAE6jhAEAgBkfpmWJAAIAADMXrp64VVHCAAAATiMDAQCAGRkISwQQAACYMQfCEiUMAADgNDIQAACYUcKwRAABAIAZJQxLBBAAAJiRgbDEHAgAAOA0MhAAAJhRwrBEAAEAgBklDEuUMAAAgNPIQAAAYEYGwhIBBAAAZoZR3CMo8ShhAAAAp5GBAADAjBKGJQIIAADMCCAsUcIAAABOIwMBAIAZD5KyRAABAIAZJQxLBBAAAJixjNMScyAAAIDTyEAAAGBGCcMSAQQAAGYEEJYoYQAAAKeRgQAAwIxlnJYIIAAAMDHyWIVhhRIGAABwGhkIAADMmERpiQACAAAz5kBYooQBAACcRgYCAAAzJlFaIoAAAMCMORCWCCAAADAjgLDEHAgAAOA0MhAAAJjxcd6WCCAAADCjhGGJEsYtaOvu/Yof97oeiBmsuu2f0NrvtjrsNwxDb/37Q7XuFa+Gjzypp0a9oqMnku37t/ywT3XbP1Ho9uOBXwpc79jJFDXq2l9Nug8s8vcGFCVfXx+9/to/dOjgJv2e/rO+Wb9cDSPq2fe/8/abysk+4bD957OFxThioPiQgbgFXbyUpTuqV1aXB1tq6ISpBfb/a+l/tPjT1Zrw7ABVDK2gt/79oQaOnqTlc16RZ+nSqn9XTX21aLrDMW+9+6G+37lHd99RzaH9ck6ORrwyQ/fefYd27fu5SN8XUNTmznlNd999p/r0HaKTv6UopldXfbHqfdWt11onT14JsletWqd+/RPsx2RlZRfXcFGUWMZpiQzELaj5ffU0JPZRtWnasMA+wzC08JNVGtDzYT0QGaE7q1XWxOEDdepMmtZ9t02S5OHhrqDAAPvm7+err5K2qXPbFrLZbA7nm75gmapVCld0i0Z/ynsDioqXl5e6dumgUaNe1jffbtKhQ0f04ktv6OdDR/S3gb3t/bKys5WScsq+paWlF+OoUWSMPNdttyinA4jTp09r0qRJ6tKliyIjIxUZGakuXbpo8uTJOnXqVFGMES50PPmUTp9LV+MGdextZX3KqO6d1bVrf+EZhK+/36G038+rc9sWDu2bdu7R6m8364VBsUU6ZuDP4O5eSu7u7rp0Kcuh/dLFS2ra5D7765YtInXy+C7t+XGD3pqeqMDAcn/2UIESwakAYsuWLbrjjjs0bdo0+fv7q0WLFmrRooX8/f01bdo01apVS1u3brU8T1ZWljIyMhw20oB/jjPn0iRJ5cv5O7SXL+ev0+cK/0vqoy++VpN76yq0QqC9LS3jd41+45+akDBAvj7eRTZe4M9y/nymkpK26oW/P6OwsBC5ubmpV6+uatw4QqFhIZKkL1Z/pT5PPqMH2/2fRv39ZbVo0Vj/+exdubmRzL3l5Bmu225RTs2BGDx4sB599FHNnj27QCrbMAz97W9/0+DBg5WUlHTd8yQmJuof//iHQ9voIU9pzDP9nRkO/gTJp87qu+279dqowQ7t46f+Sx1aRaph3VrFNDLA9WL7DtHbc1/Xr0e3KycnRzt27Nb7H3yie++9R5K0ZMmn9r4//rhfu3fv08EDSWrVsonWffVtcQ0bRcBgFYYlpwKIXbt2af78+QWCB0my2WwaNmyYGjRoYHmeUaNGKSEhwaHNduIHZ4aCP6h8uQBJ0plz6aoQGGBvP3MuXbVqVCnQ/5M1GxRQ1letGjt+XTfv2quvv9+uBR9+LkkyZCgvz1D9jrEaN+RJdYluWWTvASgqv/xyVA9EdVeZMt7y8yur5ORULV40S4d/OVZo/8OHj+nUqTOqUaMqAQT+5zgVQISGhmrz5s2qVavwvzo3b96skJAQy/N4enrK09PToS37dGlnhoI/6LbQCgoq569NO/fYA4bzmRe1+8Av+r+ObRz6GoahT9ZsUKc2zeTh7vitsvCNscq9KkL/Kmm7/rV0hd59Y6yCywcK+Cu7cOGiLly4qIAAfz3YtqVGjnq50H4VK4apfPly+i055U8eIYrcLVx6cBWnAojhw4drwIAB2rZtm9q0aWMPFlJSUrR27Vr985//1GuvvVYkA8WNu3Dxko6d/O8vtBMpp7T/0FH5l/VRWHCQHu/cTnPeX67KFUNVMaSC3np3mSqUD9ADTSIczrNp516dSD6lru1aFbhG9coVHV7vOXhYbm5uqlm1UpG8J+DP8GDblrLZbDrw0yHdXqOqXnlljA4cOKT5Cz6Qj08ZjR2doI8+/lzJKamqUb2qEhNf0M+Hjmj16vXFPXS42i28esJVnAog4uLiFBQUpDfffFMzZ85Ubm6uJKlUqVKKiIjQ/Pnz1aNHjyIZKG7cnoOH9eTzE+2vJ89dLEl6OKqZXn52oJ58tKMuXsrSP6b9S7+fv6AGd9+h2S89J8/Sjlmgj1avV/3aNVW9UvifOn6guPj5++nll0bqttvCdPZsmj76+HONGfuqcnJy5O7urrp179ITTzyqgAA/nTyZojVfrte48ZOVnc0k8FsOGQhLNsP4Yw/8vnz5sk6fPi1JCgoKkoeHx00NJPuXzTd1PHArKlOrS3EPASiRcrJPFOn5M1+Mcdm5fMYuctm5SpI//CRKDw8PhYWFuXIsAACUDKzCsMSjrAEAMKOEYYmnnwAAAKeRgQAAwIxVGJYIIAAAMKOEYYkSBgAAcBoZCAAATPgsDGsEEAAAmFHCsEQJAwAAOI0MBAAAZmQgLBFAAABgxjJOS5QwAAAwyzNctzkhNzdXY8aMUbVq1eTt7a0aNWropZde0tUfW2UYhsaOHauwsDB5e3srKipKBw8edDjP2bNnFRMTIz8/PwUEBKhfv346f/68S25NPgIIAABKiFdffVWzZs3SW2+9pX379unVV1/VpEmTNH36dHufSZMmadq0aZo9e7Y2bdokHx8fRUdH69KlS/Y+MTEx2rNnj9asWaMVK1Zow4YNGjBggEvH+oc/jdPV+DROoCA+jRMoXFF/GufvQzu57Fxlp3x2w30feughhYSE6J133rG3devWTd7e3lq4cKEMw1B4eLieffZZDR8+XJKUnp6ukJAQzZ8/Xz179tS+fftUu3ZtbdmyRQ0bNpQkrVq1Sh06dNDx48cVHh7ukvdFBgIAADMXljCysrKUkZHhsGVlZRV62SZNmmjt2rX66aefJEm7du3St99+q/bt20uSDh8+rOTkZEVFRdmP8ff3V6NGjZSUlCRJSkpKUkBAgD14kKSoqCi5ublp06ZNLrtFBBAAABShxMRE+fv7O2yJiYmF9h05cqR69uypWrVqycPDQw0aNNDQoUMVExMjSUpOTpYkhYSEOBwXEhJi35ecnKzg4GCH/e7u7goMDLT3cQVWYQAAYObCJ1GOGjVKCQkJDm2enp6F9l2yZIkWLVqkxYsX6+6779bOnTs1dOhQhYeHKzY21mVjcgUCCAAAzFz4HAhPT89rBgxmzz33nD0LIUl169bV0aNHlZiYqNjYWIWGhkqSUlJSFBYWZj8uJSVF9evXlySFhoYqNTXV4bw5OTk6e/as/XhXoIQBAEAJceHCBbm5Of7TXKpUKeX9/4xItWrVFBoaqrVr19r3Z2RkaNOmTYqMjJQkRUZGKi0tTdu2bbP3WbdunfLy8tSoUSOXjZUMBAAAZsX0JMpOnTrp5ZdfVuXKlXX33Xdrx44deuONN/Tkk09Kkmw2m4YOHaoJEyaoZs2aqlatmsaMGaPw8HB17txZknTXXXepXbt26t+/v2bPnq3Lly8rPj5ePXv2dNkKDIkAAgCAAorrCQfTp0/XmDFjNGjQIKWmpio8PFwDBw7U2LFj7X1GjBihzMxMDRgwQGlpaWrWrJlWrVolLy8ve59FixYpPj5ebdq0kZubm7p166Zp06a5dKw8BwIowXgOBFC4on4ORMbAaJedy2/OFy47V0lCBgIAADM+TMsSAQQAAGYEEJYIIAAAMDEIICyxjBMAADiNDAQAAGZkICwRQAAAYOa6J1nfsihhAAAAp5GBAADAhEmU1gggAAAwI4CwRAkDAAA4jQwEAABmTKK0RAABAIAJcyCsUcIAAABOIwMBAIAZJQxLBBAAAJhQwrBGAAEAgBkZCEvMgQAAAE4jAwEAgIlBBsISAQQAAGYEEJYoYQAAAKeRgQAAwIQShjUCCAAAzAggLFHCAAAATiMDAQCACSUMawQQAACYEEBYI4AAAMCEAMIacyAAAIDTyEAAAGBm2Ip7BCUeAQQAACaUMKxRwgAAAE4jAwEAgImRRwnDCgEEAAAmlDCsUcIAAABOIwMBAICJwSoMSwQQAACYUMKwRgkDAAA4jQwEAAAmrMKwRgABAICJYRT3CEo+AggAAEzIQFhjDgQAAHAaGQgAAEzIQFgjgAAAwIQ5ENYoYQAAAKeRgQAAwIQShjUCCAAATHiUtTVKGAAAwGlkIAAAMOGzMKwRQAAAYJJHCcMSJQwAAOA0MhAAAJgwidIaAQQAACYs47RGAAEAgAlPorTGHAgAAOA0MhAAAJhQwrBGAAEAgAnLOK1RwgAAAE4jAwEAgAnLOK0RQAAAYMIqDGuUMAAAgNPIQAAAYMIkSmsEEAAAmDAHwholDAAA4DQCCAAATAzDdZuzTpw4occff1zly5eXt7e36tatq61bt141NkNjx45VWFiYvL29FRUVpYMHDzqc4+zZs4qJiZGfn58CAgLUr18/nT9//mZviwMCCAAATPIMm8s2Z5w7d05NmzaVh4eHVq5cqb179+r1119XuXLl7H0mTZqkadOmafbs2dq0aZN8fHwUHR2tS5cu2fvExMRoz549WrNmjVasWKENGzZowIABLrs/kmQzjJKxWMWjdMXiHgJQ4lw4+U1xDwEokTyCqhfp+bdU7OKyc9134uMb7jty5Eht3LhR33xT+M++YRgKDw/Xs88+q+HDh0uS0tPTFRISovnz56tnz57at2+fateurS1btqhhw4aSpFWrVqlDhw46fvy4wsPDb/5NiQwEAABFKisrSxkZGQ5bVlZWoX0//fRTNWzYUI8++qiCg4PVoEED/fOf/7TvP3z4sJKTkxUVFWVv8/f3V6NGjZSUlCRJSkpKUkBAgD14kKSoqCi5ublp06ZNLntfBBAAAJi4soSRmJgof39/hy0xMbHQ6/7yyy+aNWuWatasqS+++EJPP/20hgwZogULFkiSkpOTJUkhISEOx4WEhNj3JScnKzg42GG/u7u7AgMD7X1cgWWcAACYuLK2P2rUKCUkJDi0eXp6Fto3Ly9PDRs21MSJEyVJDRo00I8//qjZs2crNjbWhaO6eWQgAAAoQp6envLz83PYrhVAhIWFqXbt2g5td911l44dOyZJCg0NlSSlpKQ49ElJSbHvCw0NVWpqqsP+nJwcnT171t7HFQggAAAwKa5VGE2bNtWBAwcc2n766SdVqVJFklStWjWFhoZq7dq19v0ZGRnatGmTIiMjJUmRkZFKS0vTtm3b7H3WrVunvLw8NWrU6I/ekgIoYQAAYFJcT6IcNmyYmjRpookTJ6pHjx7avHmz5s6dq7lz50qSbDabhg4dqgkTJqhmzZqqVq2axowZo/DwcHXu3FnSlYxFu3bt1L9/f82ePVuXL19WfHy8evbs6bIVGBIBBAAAJcZ9992njz/+WKNGjdKLL76oatWqacqUKYqJibH3GTFihDIzMzVgwAClpaWpWbNmWrVqlby8vOx9Fi1apPj4eLVp00Zubm7q1q2bpk2b5tKx8hwIoATjORBA4Yr6ORDfhHZ32bmaJy9z2blKEjIQAACYGOLDtKwwiRIAADiNDAQAACZ5JaK4X7IRQAAAYJJHCcMSAQQAACbMgbDGHAgAAOA0MhAAAJjkFfcA/gIIIAAAMKGEYY0SBgAAcBoZCAAATChhWCOAAADAhADCGiUMAADgNDIQAACYMInSGgEEAAAmecQPlihhAAAAp5GBAADAhM/CsEYAAQCACR/GaY0AAgAAE5ZxWmMOBAAAcBoZCAAATPJszIGwQgABAIAJcyCsUcIAAABOIwMBAIAJkyitEUAAAGDCkyitUcIAAABOIwMBAIAJT6K0RgABAIAJqzCsUcIAAABOIwMBAIAJkyitEUAAAGDCMk5rBBAAAJgwB8IacyAAAIDTyEAAAGDCHAhrBBAAAJgwB8IaJQwAAOA0MhAAAJiQgbBGAAEAgInBHAhLlDAAAIDTyEAAAGBCCcMaAQQAACYEENYoYQAAAKeRgQAAwIRHWVsjgAAAwIQnUVojgAAAwIQ5ENaYAwEAAJxGBgIAABMyENYIIAAAMGESpTVKGAAAwGlkIAAAMGEVhjUCCAAATJgDYY0SBgAAcBoZCAAATJhEaY0AAgAAkzxCCEuUMAAAgNPIQAAAYMIkSmsEEAAAmFDAsEYAAQCACRkIa8yBAAAATiMDAQCACU+itEYAAQCACcs4rVHCAAAATiMDAQCACfkHa2QgAAAwyXPh9ke98sorstlsGjp0qL3t0qVLiouLU/ny5eXr66tu3bopJSXF4bhjx46pY8eOKlOmjIKDg/Xcc88pJyfnJkZSOAIIAABKmC1btmjOnDm65557HNqHDRumzz77TEuXLtX69et18uRJde3a1b4/NzdXHTt2VHZ2tr777jstWLBA8+fP19ixY10+RgIIAABM8mS4bMvKylJGRobDlpWVdc1rnz9/XjExMfrnP/+pcuXK2dvT09P1zjvv6I033tADDzygiIgIzZs3T999952+//57SdLq1au1d+9eLVy4UPXr11f79u310ksvacaMGcrOznbpPSKAAADAxHDhlpiYKH9/f4ctMTHxmteOi4tTx44dFRUV5dC+bds2Xb582aG9Vq1aqly5spKSkiRJSUlJqlu3rkJCQux9oqOjlZGRoT179tzMLSmASZQAABShUaNGKSEhwaHN09Oz0L7vv/++tm/fri1bthTYl5ycrNKlSysgIMChPSQkRMnJyfY+VwcP+fvz97kSAQQAACaufJS1p6fnNQOGq/3666965plntGbNGnl5eblwBEWDEgYAACaunANxo7Zt26bU1FTde++9cnd3l7u7u9avX69p06bJ3d1dISEhys7OVlpamsNxKSkpCg0NlSSFhoYWWJWR/zq/j6sQQAAAYOLKORA3qk2bNtq9e7d27txp3xo2bKiYmBj7/3t4eGjt2rX2Yw4cOKBjx44pMjJSkhQZGandu3crNTXV3mfNmjXy8/NT7dq1/9jNuAZKGAAAlABly5ZVnTp1HNp8fHxUvnx5e3u/fv2UkJCgwMBA+fn5afDgwYqMjFTjxo0lSQ8++KBq166tJ554QpMmTVJycrJGjx6tuLi4GyqjOIMAAgAAk5L6cd5vvvmm3Nzc1K1bN2VlZSk6OlozZ8607y9VqpRWrFihp59+WpGRkfLx8VFsbKxefPFFl4/FZhhGiXhip0fpisU9BKDEuXDym+IeAlAieQRVL9LzD6n6fy4717QjH7jsXCUJcyAAAIDTKGEAAGBSUksYJQkBBAAAJs4sv/xfRQkDAAA4jQwEAAAm5B+sEUAAAGBCCcMaJYz/Ac2aNdLHH8/X0SPbdDn7hB5+OLpAn3HjhuvY0e3KSP9Zq1a+r9tvr+awv1y5AP17wXSdOb1fp1L3au6c1+TjU+bPegvATdu6c7fiRoxT64djVKdpe63d8J3D/jVfb1T/oX9X0/Y9VKdpe+3/6VCBcyxd/rn6xI9Qo7ZdVadpe2X8ft5h/4nfUjQm8U1Fd++jiNaPqN2jffXW2+/q8uXLRfregOJAAPE/wMenjH74Ya+GPPNCofuHDx+k+LgnFRc/Uk2bdVLmhQv6z4pFDk8t+/eC6apd+061b/+YOneOVbNmjTVr1qQ/6y0AN+3ixUu68/bqeuHZQYXvv3RJ995zt4Y9/eQ1z3HpUpaaNWqo/r17Frr/8NFfZeQZGvvcYH2ycLaeHzJQSz75XFPmzHfFW8CfKM+F262KEsb/gC+++EpffPHVNfcPGfyUJiZO1WefrZYk9e37jE4c36lHHonWkiWfqlat29Wu3QNq3Li9tm3/QZI0dNhoffbpu3r++Zf0228p1zw3UFI0j7xPzSPvu+b+h9u1kXQli3AtT/xfF0nS5v//c2DWrHFDNWvc0P66UsUwHT52XEs++Y+ei+//R4aNYmJQwrBEBuJ/XLVqlRUWFqJ16761t2Vk/K7Nm3eocaMISVLjRhE6dy7NHjxI0tq13ygvL0/339/gTx8z8FdyPjNTfmXLFvcw4CQyENZcHkD8+uuvevLJa6cAJSkrK0sZGRkOWwl5ovb/nNCQYElSSsoph/aU1NMKCb2yLyQ0WKmnzjjsz83N1dmzafbjARR07PhJLV72qXp0bl/cQwFczuUBxNmzZ7VgwYLr9klMTJS/v7/Dlpf3u6uHAgDFJuXUaQ1MGK0HWzdX94cJIP5qDBf+d6tyeg7Ep59+et39v/zyi+U5Ro0apYSEBIe2wPK1nB0KXCA55cpnxoeEVFBy8n8/Pz4kOEi7du2RJKUkpyq4QnmH40qVKqXAwAD78QD+K/XUGT05eKTq162t8c8PKe7h4A+4lUsPruJ0ANG5c2fZbLbrlhxsNtt1z+Hp6Vngc8mtjkHROHz4mH77LUWtWzezBwxly/rq/vsbaM7cf0uSvt+0TeXKBejeBnW1fcduSVLr1k3l5uamzZt3FNvYgZIo5dRpPTl4pGrfebsm/H2Y3NyYaoZbk9MBRFhYmGbOnKlHHnmk0P07d+5URETETQ8MruPjU8bhuQ7VqlZWvXp36+zZc/r115OaNv1t/X3UEP388y86cuRXjR//nE6eTNHy5V9Ikvbv/1mrVq3T7NmTFRc3Uh4e7po69WV9sGQ5KzDwl3HhwkUdO37S/vrEyRTt/+mQ/P3KKiw0WOkZv+u35FSlnr4y3+fwseOSpKDy5RRUPlCSdPrMWZ0+c85+noOHjsinjLfCQoPl71dWKadOq2/88woPDdbw+Kd0Li3dfr38c+CvIY95eZZshpOzFx9++GHVr19fL774YqH7d+3apQYNGigvz7kEkEfpik71x41r0SJSa79cVqD93/9eon5PDZN05UFST/WLUUCAnzZu3KLBQ/6ugwf/W44qVy5AU6dO0EMd2yovL08ff/y5hg4bo8zMC3/a+/hfdOHkN8U9hFvG5u0/6MnBzxdof6R9lF4e/aw++c8ajZ74RoH9Tz8Zo7h+j0uSZryzULP+tahAnwl/T1Dnjm2veQ5J+nHjypt8B7iaR1D1Ij3/41W6uuxcC49+5LJzlSROBxDffPONMjMz1a5du0L3Z2ZmauvWrWrZsqVTAyGAAAoigAAKRwBR/JwuYTRv3vy6+318fJwOHgAAKEn4LAxrPIkSAACTW3n5paswPRgAADiNDAQAACY8B8IaAQQAACbMgbBGAAEAgAlzIKwxBwIAADiNDAQAACbMgbBGAAEAgImTz1j8n0QJAwAAOI0MBAAAJqzCsEYAAQCACXMgrFHCAAAATiMDAQCACc+BsEYAAQCACXMgrFHCAAAATiMDAQCACc+BsEYAAQCACaswrBFAAABgwiRKa8yBAAAATiMDAQCACaswrBFAAABgwiRKa5QwAACA08hAAABgQgnDGgEEAAAmrMKwRgkDAAA4jQwEAAAmeUyitEQAAQCACeGDNUoYAADAaWQgAAAwYRWGNQIIAABMCCCsEUAAAGDCkyitMQcCAAA4jQwEAAAmlDCsEUAAAGDCkyitUcIAAABOIwMBAIAJkyitEUAAAGDCHAhrlDAAAIDTyEAAAGBCCcMaAQQAACaUMKxRwgAAAE4jAwEAgAnPgbBGAAEAgEkecyAsUcIAAMDEcOF/zkhMTNR9992nsmXLKjg4WJ07d9aBAwcc+ly6dElxcXEqX768fH191a1bN6WkpDj0OXbsmDp27KgyZcooODhYzz33nHJycm76vlyNAAIAgBJi/fr1iouL0/fff681a9bo8uXLevDBB5WZmWnvM2zYMH322WdaunSp1q9fr5MnT6pr1672/bm5uerYsaOys7P13XffacGCBZo/f77Gjh3r0rHajBKyVsWjdMXiHgJQ4lw4+U1xDwEokTyCqhfp+e8Kvt9l59qXuvkPH3vq1CkFBwdr/fr1atGihdLT01WhQgUtXrxY3bt3lyTt379fd911l5KSktS4cWOtXLlSDz30kE6ePKmQkBBJ0uzZs/X888/r1KlTKl26tEveFxkIAABMXFnCyMrKUkZGhsOWlZV1Q+NIT0+XJAUGBkqStm3bpsuXLysqKsrep1atWqpcubKSkpIkSUlJSapbt649eJCk6OhoZWRkaM+ePa66RQQQAAAUpcTERPn7+ztsiYmJlsfl5eVp6NChatq0qerUqSNJSk5OVunSpRUQEODQNyQkRMnJyfY+VwcP+fvz97kKqzAAADBx5SqMUaNGKSEhwaHN09PT8ri4uDj9+OOP+vbbb102FlcigAAAwMSVz4Hw9PS8oYDhavHx8VqxYoU2bNig2267zd4eGhqq7OxspaWlOWQhUlJSFBoaau+zebPjvIv8VRr5fVyBEgYAACWEYRiKj4/Xxx9/rHXr1qlatWoO+yMiIuTh4aG1a9fa2w4cOKBjx44pMjJSkhQZGandu3crNTXV3mfNmjXy8/NT7dq1XTZWMhAAAJgU14Ok4uLitHjxYi1fvlxly5a1z1nw9/eXt7e3/P391a9fPyUkJCgwMFB+fn4aPHiwIiMj1bhxY0nSgw8+qNq1a+uJJ57QpEmTlJycrNGjRysuLs7pTMj1sIwTKMFYxgkUrqiXcVYPauCyc/1yescN97XZbIW2z5s3T3369JF05UFSzz77rN577z1lZWUpOjpaM2fOdChPHD16VE8//bS+/vpr+fj4KDY2Vq+88orc3V2XNyCAAEowAgigcLdqAPFXQgkDAAATw8gr7iGUeAQQAACY5PFpnJYIIAAAMCkh1f0SjWWcAADAaWQgAAAwoYRhjQACAAATShjWKGEAAACnkYEAAMCkuJ5E+VdCAAEAgIkrP0zrVkUJAwAAOI0MBAAAJkyitEYAAQCACcs4rVHCAAAATiMDAQCACSUMawQQAACYsIzTGgEEAAAmZCCsMQcCAAA4jQwEAAAmrMKwRgABAIAJJQxrlDAAAIDTyEAAAGDCKgxrBBAAAJjwYVrWKGEAAACnkYEAAMCEEoY1AggAAExYhWGNEgYAAHAaGQgAAEyYRGmNAAIAABNKGNYIIAAAMCGAsMYcCAAA4DQyEAAAmJB/sGYzyNPgKllZWUpMTNSoUaPk6elZ3MMBSgR+LoCCCCDgICMjQ/7+/kpPT5efn19xDwcoEfi5AApiDgQAAHAaAQQAAHAaAQQAAHAaAQQceHp6aty4cUwUA67CzwVQEJMoAQCA08hAAAAApxFAAAAApxFAAAAApxFAAAAApxFAAAAApxFAwG7GjBmqWrWqvLy81KhRI23evLm4hwQUqw0bNqhTp04KDw+XzWbTJ598UtxDAkoMAghIkj744AMlJCRo3Lhx2r59u+rVq6fo6GilpqYW99CAYpOZmal69eppxowZxT0UoMThORCQJDVq1Ej33Xef3nrrLUlSXl6eKlWqpMGDB2vkyJHFPDqg+NlsNn388cfq3LlzcQ8FKBHIQEDZ2dnatm2boqKi7G1ubm6KiopSUlJSMY4MAFBSEUBAp0+fVm5urkJCQhzaQ0JClJycXEyjAgCUZAQQAADAaQQQUFBQkEqVKqWUlBSH9pSUFIWGhhbTqAAAJRkBBFS6dGlFRERo7dq19ra8vDytXbtWkZGRxTgyAEBJ5V7cA0DJkJCQoNjYWDVs2FD333+/pkyZoszMTPXt27e4hwYUm/Pnz+vnn3+2vz58+LB27typwMBAVa5cuRhHBhQ/lnHC7q233tLkyZOVnJys+vXra9q0aWrUqFFxDwsoNl9//bVat25doD02Nlbz58//8wcElCAEEAAAwGnMgQAAAE4jgAAAAE4jgAAAAE4jgAAAAE4jgAAAAE4jgAAAAE4jgAAAAE4jgAAAAE4jgAAAAE4jgAAAAE4jgAAAAE77fz2LUb508x2+AAAAAElFTkSuQmCC\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Treinando KNN...\n", "<PERSON><PERSON>r<PERSON><PERSON> média (KNN - validação cruzada): 0.8227\n", "\n", "Avaliação do modelo KNN:\n", "Acurácia: 0.7942\n", "Precisão: 0.9065\n", "Recall: 0.6642\n", "F1-Score: 0.7667\n", "AUC-ROC: 0.8846\n", "<PERSON><PERSON>:\n", "[[1086   83]\n", " [ 407  805]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAHHCAYAAABDUnkqAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAXihJREFUeJzt3Xl4TGf/BvB7ZpJM9skmGyGInVpLUdX+Gk3LG7RVqTXUUqWo2NfQ1vqiFK1Se6nYX1u1RKm1WkurtQtiS4jsezLz/P6IjIwkzImZTGZyf65rrjc5c86Z7xzq3O9znkUmhBAgIiIishByUxdAREREZEgMN0RERGRRGG6IiIjIojDcEBERkUVhuCEiIiKLwnBDREREFoXhhoiIiCwKww0RERFZFIYbIiIisigMN0RERGRRGG6IzNz169fx8ccfo1q1arC1tYWzszNat26NhQsXIiMjw9TlSXbo0CHIZDLtS6FQwNPTE126dMHFixeLPW737t14++234e7uDltbW9SsWROjRo3Co0ePnvlZ7733Hry9vWFjYwNPT08EBwdj27ZtBv1Or7/+OurXr19oe2RkJOzt7dGkSRPEx8cDAPz9/SGTyTB06NAi65XJZNiyZYt22+rVqyGTyWBra4u7d+/q/dlElozhhsiM7dmzBw0aNMCmTZsQHByMRYsWYebMmahcuTJGjx6N4cOHm7rEEhs2bBjWrVuH77//Hj169MCePXvQpk0bxMTEFNp31KhRCA4ORkxMDMaOHYvFixcjMDAQixcvRsOGDXH58uVCx4SHh+ONN97AP//8g48//hhLly7F6NGjkZqaivfffx8bNmww6vc7ePAggoODUatWLRw4cABubm467y9fvhz37t3T+3xZWVmYNWuWocskMk+CiMxSVFSUcHR0FLVr1xb37t0r9P7Vq1fFggULDPJZqampBjmPPn799VcBQGzevFln+7fffisAiNmzZ+ts37BhgwAgQkJCRG5urs57v//+u7C3txcNGjQQOTk52u2bN28WAESXLl1EdnZ2oRr27dsndu3aZbDv1LZtW1GvXj3t74cOHRL29vaiYcOGIi4uTmffKlWqiHr16gkrKysxdOhQnfeKujarVq0SAESjRo2EUqkUd+/efeZnE5UHbLkhMlNz5sxBamoqVqxYAR8fn0LvBwQEaFtubt68CZlMhtWrVxfaTyaTYerUqdrfp06dCplMhgsXLqB79+5wdXXFq6++irlz50Imk+HWrVuFzjF+/HjY2NggISEBAHDkyBF88MEHqFy5MpRKJfz8/DBixIgXekzWpk0bAHmP4QqaNm0aXF1dsWzZMigUCp33mjdvjrFjx+L8+fM6j3ImT54MNzc3rFy5EtbW1oU+KygoCP/5z39KXOuzHDlyBB06dEBAQAAOHDgAd3f3Qvv4+/ujd+/eklpvJkyYALVazdYbIvCxFJHZ2rVrF6pVq4ZWrVoZ5fwffPAB0tPTMWPGDAwYMABdu3aFTCbDpk2bCu27adMmvPXWW3B1dQUAbN68Genp6fjkk0+waNEiBAUFYdGiRejdu3eJ67l58yYAaD8DAK5evYrLly+jU6dOcHZ2LvK4/M/cvXu39phLly6hc+fOcHJyKnE9JXHs2DG0b98eVatWRWRkJDw8PIrdd+LEicjNzdU7rFStWlVyICKyVFamLoCIpEtOTsbdu3fRqVMno31Gw4YNC/U7eeWVVxAREYHRo0drt/3xxx+IiorSaf2ZPXs27OzstL8PHDgQAQEBmDBhAqKjo1G5cuXnfn5KSgri4uKQk5ODv//+G5999hlkMhnef/997T4XLlzQ1locf39/ODs7azsj5/9vgwYNnluDId2/fx/vvPMOqlSpgoMHDz4z2ABAtWrV0KtXLyxfvhzjx48vsnXuaRMnTsTatWsxe/ZsLFy40FClE5kdttwQmaHk5GQAMGrLw6BBgwptCwkJwenTp3UeDUVERECpVOoErYLBJi0tDXFxcWjVqhWEEDh79qxen//RRx+hQoUK8PX1xdtvv42kpCSsW7cOL7/8snaflJQUAM+/Dk5OTtprVhrXrihpaWlISUmBl5dXsa1MT5s0aZKk1pv8QLRs2TLcv3//RcolMmsMN0RmKP/mmH9zN4aqVasW2vbBBx9ALpcjIiICACCEwObNm/HOO+/o3LCjo6PRp08fuLm5wdHRERUqVEDbtm0BAElJSXp9/pQpU7B//35s374dvXv3RlJSEuRy3X+y8gPK865DSkqKdl9DXLv4+HjExMRoX/p8p4CAAMyePRsHDx5Et27doFarn3tMScKK1EBEZIkYbojMkLOzM3x9ffHPP//otb9MJity+7NusAVbX/L5+vqiTZs22n43J0+eRHR0NEJCQnTO2a5dO+zZswdjx47Fjh07sH//fm1nZo1Go1fNDRo0QGBgIDp37ow1a9agY8eOGDBgAG7fvq3dp06dOgCAv//+u9jz3Lp1C8nJyahbty4AoHbt2gCA8+fP61VHUd577z34+PhoX/oOuR8zZgzGjBmD7du3Y8CAARBCPPeY/L43s2fP1uszqlWrhp49e7L1hso1hhsiM/Wf//wH169fx4kTJ567b34n3MTERJ3tRY18ep6QkBD89ddfuHz5MiIiImBvb4/g4GDt++fPn8eVK1cwb948jB07Fp06dUJgYCB8fX0lf1ZBs2bNQmZmJqZPn67dVrNmTdSsWRM7duwotiVm7dq1AKAd/VSzZk3UqlUL//vf/5CamlqiWubNm4f9+/drX2PGjNH72NmzZ6N///5YtWoVRo0a9dz9q1evjp49e+K7776T3HqjbyAisjQMN0RmasyYMXBwcED//v0RGxtb6P3r169rO5U6OzvDw8MDv/32m84+33zzjeTPff/996FQKPDjjz9i8+bN+M9//gMHBwft+/nDsQu2SgghXriDa/Xq1fH+++9j9erVOhP5TZkyBQkJCRg0aFChlqjTp09j9uzZqF+/vk5H5GnTpuHRo0fo378/cnNzC33WL7/8oh1dVZSmTZsiMDBQ+8pvFdLXd999hy5dumD+/Pn48ssvn7v/pEmTkJOTgzlz5uh1/oKBqKhJD4ksHUdLEZmp6tWrY8OGDQgJCUGdOnXQu3dv1K9fH9nZ2Th+/Dg2b96MPn36aPfv378/Zs2ahf79+6NZs2b47bffcOXKFcmf6+npiTfeeAPz589HSkqKziMpIO+xT/Xq1TFq1CjcvXsXzs7O2Lp1q3YOnBcxevRobNq0CQsWLND2KenRowf++OMPLFy4EBcuXECPHj3g6uqKM2fOYOXKlXB3d8eWLVt05rMJCQnB+fPnMX36dJw9exbdunVDlSpV8OjRI+zbtw+RkZFGnaFYLpdj/fr1SEpK0s65M3jw4GL3zw8ra9as0fszJk6ciHXr1uHy5cuoV6+eIcomMh8mnUKQiF7YlStXxIABA4S/v7+wsbERTk5OonXr1mLRokUiMzNTu196erro16+fUKlUwsnJSXTt2lU8ePBAABDh4eHa/cLDwwUA8fDhw2I/c/ny5QKAcHJyEhkZGYXev3DhgggMDBSOjo7Cw8NDDBgwQPz1118CgFi1atUzv09xMxTne/3114Wzs7NITEzU2b5jxw7Rrl074erqKpRKpQgICBAjR4585veIjIwUnTp1Ep6ensLKykpUqFBBBAcHi//973/PrFGq4mYJTk1NFa+88oqQy+Vi/fr1Qoi8GYo7dOhQaN+rV68KhUJR7AzFf/zxR6FjQkNDBQDOUEzljkwIPXq0EREREZkJ9rkhIiIii8JwQ0RERBaF4YaIiIgsCsMNERERWRSGGyIiIrIoDDdERERkUcrdJH4ajQb37t2Dk5NTsevtEBERUdkihEBKSgp8fX0LLaL7tHIXbu7duwc/Pz9Tl0FEREQlcPv2bVSqVOmZ+5S7cOPk5AQg7+I4OzubuBoiIiLSR3JyMvz8/LT38Wcpd+Em/1GUs7Mzww0REZGZ0adLCTsUExERkUVhuCEiIiKLwnBDREREFqXc9bnRl1qtRk5OjqnLIDNmY2Pz3OGKRERkeAw3TxFCICYmBomJiaYuhcycXC5H1apVYWNjY+pSiIjKFYabp+QHG09PT9jb23OiPyqR/Mki79+/j8qVK/PvERFRKWK4KUCtVmuDjbu7u6nLITNXoUIF3Lt3D7m5ubC2tjZ1OURE5QY7BBSQ38fG3t7exJWQJch/HKVWq01cCRFR+cJwUwQ+QiBD4N8jIiLTYLghIiIii2LScPPbb78hODgYvr6+kMlk2LFjx3OPOXToEJo0aQKlUomAgACsXr3a6HUSERGR+TBpuElLS0PDhg2xZMkSvfa/ceMGOnTogDfeeAPnzp3DZ599hv79++Pnn382cqXm48SJE1AoFOjQoUOh9w4dOgSZTFbkMHd/f38sWLBAZ9uvv/6K9u3bw93dHfb29qhbty5GjhyJu3fvGql6IDMzE0OGDIG7uzscHR3x/vvvIzY29pnHpKam4tNPP0WlSpVgZ2eHunXrYunSpTr7xMTEoFevXvD29oaDgwOaNGmCrVu3Fnm+rKwsNGrUCDKZDOfOndN5TwiBuXPnombNmlAqlahYsSKmT5/+Qt+ZiIgMy6Sjpd555x288847eu+/dOlSVK1aFfPmzQMA1KlTB0ePHsVXX32FoKAgY5VpVlasWIGhQ4dixYoVuHfvHnx9fUt0nu+++w6DBw9GaGgotm7dCn9/f0RHR2Pt2rWYN28e5s+fb+DK84wYMQJ79uzB5s2boVKp8Omnn+K9997DsWPHij0mLCwMBw8exA8//AB/f3/88ssvGDx4MHx9fdGxY0cAQO/evZGYmIidO3fCw8MDGzZsQNeuXfHnn3+icePGOucbM2YMfH198ddffxX6rOHDh+OXX37B3Llz0aBBA8THxyM+Pt6wF4GIqBQJIZCSLZCcqTHYOW0UMng6Kgx2PqnMaij4iRMnEBgYqLMtKCgIn332WbHHZGVlISsrS/t7cnKyscozudTUVERERODPP/9ETEwMVq9ejQkTJkg+z507dzBs2DAMGzYMX331lXa7v78/XnvtNaNNcJiUlIQVK1Zgw4YN+L//+z8AwKpVq1CnTh2cPHkSr7zySpHHHT9+HKGhoXj99dcBAAMHDsR3332HU6dOacPN8ePH8e2336J58+YAgEmTJuGrr77C6dOndcLNTz/9hF9++QVbt27FTz/9pPM5Fy9exLfffot//vkHtWrVAgBUrVrVoNeAiMgQhBBIzhJ4mKZGXJoGcWkaPEwv8HOaGnHpmse/q5Fl4EGdTXytsb1XBcOeVAKzCjcxMTHw8vLS2ebl5YXk5GRkZGTAzs6u0DEzZ87EtGnTSvyZQghk5IgSH/8i7KxlkkbcbNq0CbVr10atWrXQs2dPfPbZZxg/frzkUTubN29GdnY2xowZU+T7Li4uxR77zjvv4MiRI8W+X6VKFfz7779Fvnf69Gnk5OToBNjatWujcuXKOHHiRLHhplWrVti5cyc++ugj+Pr64tChQ7hy5YpOMGvVqhUiIiLQoUMHuLi4YNOmTcjMzNQGIgCIjY3FgAEDsGPHjiKnA9i1axeqVauG3bt34+2334YQAoGBgZgzZw7c3NyK/c5ERIZQMLA8THsSTB6mFwgs+dvT1ciWGFiUCgAGGuRpozDtaFGzCjclMX78eISFhWl/T05Ohp+fn97HZ+QI1PkqxhilPdfFEd6wt9H/L8iKFSvQs2dPAMDbb7+NpKQkHD58WOcGro+rV6/C2dkZPj4+ko4DgO+//x4ZGRnFvv+syexiYmJgY2NTKDx5eXkhJqb4P4NFixZh4MCBqFSpEqysrCCXy7F8+XK89tpr2n02bdqEkJAQuLu7w8rKCvb29ti+fTsCAgIA5P2j0adPHwwaNAjNmjXDzZs3C31OVFQUbt26hc2bN2Pt2rVQq9UYMWIEunTpgoMHDxZbHxFRcYQQSMoU2paUh6l5wSQvrOT9/PBxiHmUrpEcWJxsZKjgKIeHvQIeDvK8l70Cng5yeDg82VbBXgFba8uZvsKswo23t3ehzqWxsbFwdnYustUGAJRKJZRKZWmUZ1KXL1/GqVOnsH37dgCAlZUVQkJCsGLFCsnhRghR4jlaKlasWKLjXsSiRYtw8uRJ7Ny5E1WqVMFvv/2GIUOGwNfXV9sKNHnyZCQmJuLAgQPw8PDAjh070LVrVxw5cgQNGjTAokWLkJKSgvHjxxf7ORqNBllZWVi7di1q1qwJIC9QNm3aFJcvX9Y+qiKi8k0IgcTMJ4+EtC0q6ZoC254ElhyJXV2clTJUyA8m9k9CSgV7OSo4Kh5vy9tua2U5gUUKswo3LVu2xN69e3W27d+/Hy1btjTaZ9pZy3BxhLfRzv+8z9bXihUrkJubq9OBWAgBpVKJxYsXQ6VSwdnZGUBe35anW0cSExOhUqkAADVr1kRSUhLu378vufXmRR5LeXt7Izs7G4mJiTr1xcbGwtu76D+DjIwMTJgwAdu3b9eOEHvppZdw7tw5zJ07F4GBgbh+/ToWL16Mf/75B/Xq1QMANGzYEEeOHMGSJUuwdOlSHDx4ECdOnCgUhJs1a4YePXpgzZo18PHxgZWVlTbYAHmd2gEgOjqa4YbIgmmEQGJGft+Vx4+Dink09Chdg9wSBpYKDk+CSX5I8SzQwuJuX34DixQmDTepqam4du2a9vcbN27g3LlzcHNzQ+XKlTF+/HjcvXsXa9euBQAMGjQIixcvxpgxY/DRRx/h4MGD2LRpE/bs2WO0GmUymaRHQ6aQm5urHcX01ltv6bzXuXNn/Pjjjxg0aBBq1KgBuVyO06dPo0qVKtp9oqKikJSUpL1pd+nSBePGjcOcOXN0+q3kezp8FPQij6WaNm0Ka2trREZG4v333weQ1yIVHR1dbIDNyclBTk4O5HLdWQ0UCgU0mrx/XdLT0wHgmft8/fXX+PLLL7Xv3bt3D0FBQYiIiECLFi0AAK1bt0Zubi6uX7+O6tWrAwCuXLkCADrXk4jMg0YIJOQHlvxHQKmaxx1tH4eX9CctLFIDi8pWBg/7vMBSoeBjIHt5gSCjgLu9HEoGFoMyabj5888/8cYbb2h/z+8bExoaitWrV+P+/fuIjo7Wvl+1alXs2bMHI0aMwMKFC1GpUiV8//335X4Y+O7du5GQkIB+/fppW1/yvf/++1ixYgUGDRoEJycn9O/fHyNHjoSVlRUaNGiA27dvY+zYsXjllVfQqlUrAICfnx+++uorfPrpp0hOTkbv3r3h7++PO3fuYO3atXB0dNQOx3/aizyWUqlU6NevH8LCwuDm5gZnZ2cMHToULVu21OlMXLt2bcycORPvvvsunJ2d0bZtW4wePRp2dnaoUqUKDh8+jLVr12qHq9euXRsBAQH4+OOPMXfuXLi7u2PHjh3Yv38/du/eDQCoXLmyTi2Ojo4AgOrVq6NSpUoAgMDAQDRp0gQfffQRFixYAI1GgyFDhqBdu3Y6rTlEZDoaIRCfrtGOBNJ5NJSuO1LoUZoGaonjRVxsZbqPgbStKrrb3B3kJu9UW57JhBCmGQpkIsnJyVCpVEhKStI+psmXmZmJGzduoGrVqrC1tTVRhdIFBwdDo9EU2YJ16tQptGjRAn/99RdeeuklZGZmYtasWYiIiMCtW7fg7e2Ndu3aYfr06fDw8NA59sCBA5g7dy5OnTqFjIwM+Pv74z//+Q/CwsJK1NlYH5mZmRg5ciR+/PFHZGVlISgoCN98843OYymZTIZVq1ahT58+API6Io8fPx6//PIL4uPjUaVKFQwcOBAjRozQ9h26evUqxo0bh6NHjyI1NRUBAQEYNWoUevXqVWQdN2/eRNWqVXH27Fk0atRIu/3evXsYOnQofvnlFzg4OOCdd97BvHnzihwtZa5/n4jKGrUmr4Xl4VOPgHQeDT3ueBufLj2wuNrJtY+AKjx+HFSwE26Fx51wGVhM61n376cx3BTAmxEZEv8+ERVPrRGILxBYCo8Oerw9XYNH6RpoShBYKhQYHZT/c4UCfVkqPH4kZM3AYhakhBuz6lBMRERlV67mySOhh6mPhzYXCCn52+LSNIjPkBZYZHgqsOS3sDjoPhqqYC+HGwNLucdwQ0RExcrVCDx6+jHQU60r+R1v49M1kNLAIgPgZi/XhhRtvxWd1hbF41FCcljJGVhIPww3RETlTI46r4VFd0iz7uig/PCSkCE9sLjnz7OiM+/K47BSYJsbAwsZCcMNEZEFyFHntbAUO2FcgVlvEzKkjWmWyx4HFvviWleehBc3OzkUDCxkYgw3RShnfazJSPj3iF5Utlrg0dOPgQrMxVIwyJQ0sOSHFG1gKTCkOT+wuDKwkJlhuCkgf4K59PT0YpdzINJXdnY2gLzJAonyZatFoVltdR4NFdiWmCktICtkgHuBSeI8nvq54DYGFrJkDDcFKBQKuLi44MGDBwAAe3v7Eq+xROWbRqPBw4cPYW9vDysr/mdm6bJydR8JFZowrsCjoaQSBpYKBUYHeTw1F0v+oyFXOznk/DeLiOHmafmTxeUHHKKSksvlqFy5MgOymcrKFcU+AirY4fZhmhrJWdICi5U8v9Pt46n5C/RlyW9hqfC4U64LAwuRZAw3T5HJZPDx8YGnpydycnJMXQ6ZMRsbm0LrWZFpZeYK3Qnjino09LjjrdTAYv1UYClqwrj8/1XZyhhYiIyI4aYYCoWCfSWIzEBmjsDDx4EkvyWlYEgp+GgoJVt6YNGdME6hM+ttfifc/MDCVjqisoHhhojKnIwcTaE1gwqGlIKPhlIlBhYbBbTBRDs1v+NTj4bs5fB0VMBZycBCZI4YboioVKRna4qYjl+j0+qSH2TSJAYWpQI6waTgXCwV7HX7sjCwEFk+hhsiKrG0bA2eniRO+2go/cmCiHHpJQssFRx0W1i0nW2f2sbAQkQFMdwQkY607Lyw8qzRQfnDnNNzJAYWK6DCU2sGFZyav+B2JxsGFiIqGYYbIgsnhEBatihywcP8x0QFg0yGxMBiayXTzmj7ZB2hJ51wPR2ftLA4MrAQUSlguCEyQ0IIpGaLp0YHaYod5pyZKy2w2FnLdIYwVyiqL8vj8OLAwEJEZQzDDVEZIYRASrbQmXOlYOdbndlu09XIypV2fntrmc50/AUXPHw6yDjYcH4eIjJfDDdERiSEQHKW0Fnw8OnRQQVbWrLU0s7vYCPTeQxUoeCaQk+t2szAQkTlBcMNkUT5gUXbT0VndJDuMOdH6SULLAVXavZ4PJw5bx2hAjPg2sthz8BCRFQIww3RM/xyNQP7r2bpDHOOS1cjW2JgccwPLEVOx687262dNQMLEdGLYLghKsaqP1MxNTK52PedbGQ6IaXgY6CC2yrYK2BrzQ63RESlheGGqAjf/5GKLw7mBZsP6tuhaUUbnQURPRhYiIjKLIYboqd893sqZhzKCzaftnTEqDZOHOpMRGRGGG6ICvj2ZApmHU4BAAxv5YgRrzLYEBGZG4YboscWn0jBf3/LCzYjWjvhs1edTFwRERGVBMMNEYCFx1Iw/2hesBnZxgnDWjHYEBGZK4YbKvcWHE3BV8fygs2Y15wwpCWDDRGROWO4oXJLCIH5R1Pw9fFUAMD4150wqAWDDRGRuWO4oXJJCIH/HknBkhN5wWbiG84Y2NzRxFUREZEhMNxQuSOEwOzDKfj297xgM/n/nNH/ZQYbIiJLwXBD5YoQAjMOJWPZqTQAwNQ3ndG3GYMNEZElYbihckMIgS8OJmPFn3nBZlqgCn2aOpi4KiIiMjSGGyoXhBD4PDIZK0/nBZsv31KhV2MGGyIiS8RwQxZPCIHwA8lYcyYv2MwMUqF7IwYbIiJLxXBDFk0jBKbsT8K6s+mQAZj5tgrdGjLYEBFZMoYbslgaITDplySsP5cXbOa844KuL9mbuiwiIjIyhhuySBohMH5fEjb+nRds5rZ3QZcGDDZEROUBww1ZHI0QGPtTEjadT4dcBszr4IL36jHYEBGVFww3ZFHUGoExPyViyz8ZkMuAr/7jgs51GWyIiMoThhuyGGqNwKi9idj2bwYUMmBhsCuC69iZuiwiIiplDDdkEXI1AmF7EvG/C3nBZlFHV3SozWBDRFQeMdyQ2cvVCIzYnYidFzNgJQcWd3TFO7UYbIiIyiuGGzJrOWqB4bsSsOdyJqzlwJJOrgiqyWBDRFSeMdyQ2cpRCwzdmYCfruQFm6XvuiEwwNbUZRERkYkx3JBZylYLfPq/BPx8NRM2irxg82Z1BhsiImK4ITOUlSsw5H8J2H8tE0oF8N27bniDwYaIiB5juCGzkpUrMGhHPA5ez4JSASx/zw1tqzHYEBHREww3ZDYycwU+3h6PQ1FZUFoBK95zQ5uqDDZERKSL4YbMQmaOwMDt8Th8Iwu2VjKseN8Nr/orTV0WERGVQQw3VOZl5GgwYFsCjtzMgp21DCvfd0OrKgw2RERUNIYbKtMycjT4aGs8jt/Khr21DKu6uOGVygw2RERUPIYbKrPSs/OCzYnobDjYyLC6ixua+zHYEBHRszHcUJmUlq3BR1vicfJ2NhxtZFj9gRtersRgQ0REz8dwQ2VOapYGfbfE49SdvGCztqs7mla0MXVZRERkJhhuqExJydKgz+Z4/Hk3G042MqwLcUdjXwYbIiLSn9zUBSxZsgT+/v6wtbVFixYtcOrUqWfuv2DBAtSqVQt2dnbw8/PDiBEjkJmZWUrVkjElZ2nQe9Mj/Hk3G85KGdZ/yGBDRETSmTTcREREICwsDOHh4Thz5gwaNmyIoKAgPHjwoMj9N2zYgHHjxiE8PBwXL17EihUrEBERgQkTJpRy5WRoyVka9I54hDP3cqCylWHDh+5o6MNgQ0RE0pk03MyfPx8DBgxA3759UbduXSxduhT29vZYuXJlkfsfP34crVu3Rvfu3eHv74+33noL3bp1e25rD5VtSZka9Ix4hLP3c+BiK8OGDz3QwJvBhoiISsZk4SY7OxunT59GYGDgk2LkcgQGBuLEiRNFHtOqVSucPn1aG2aioqKwd+9etG/fvtjPycrKQnJyss6Lyo7EDA16RDzCX/dz4Gonx4/dPFDfy9rUZRERkRkzWYfiuLg4qNVqeHl56Wz38vLCpUuXijyme/fuiIuLw6uvvgohBHJzczFo0KBnPpaaOXMmpk2bZtDayTASHgebf2Nz4GYnx4YP3VHHk8GGiIhejMk7FEtx6NAhzJgxA9988w3OnDmDbdu2Yc+ePfjiiy+KPWb8+PFISkrSvm7fvl2KFVNx4tPV6L4xDv/G5sDdXo6N3RhsiIjIMEzWcuPh4QGFQoHY2Fid7bGxsfD29i7ymMmTJ6NXr17o378/AKBBgwZIS0vDwIEDMXHiRMjlhbOaUqmEUsnJ38qSR+lqdN/4CJce5qKCQ16LTU0PBhsiIjIMk7Xc2NjYoGnTpoiMjNRu02g0iIyMRMuWLYs8Jj09vVCAUSgUAAAhhPGKJYOJS1Oj249Pgs2PDDZERGRgJp3ELywsDKGhoWjWrBmaN2+OBQsWIC0tDX379gUA9O7dGxUrVsTMmTMBAMHBwZg/fz4aN26MFi1a4Nq1a5g8eTKCg4O1IYfKroePg83VR7nwdJRj44ceqO7OeSSJiMiwTHpnCQkJwcOHDzFlyhTExMSgUaNG2Ldvn7aTcXR0tE5LzaRJkyCTyTBp0iTcvXsXFSpUQHBwMKZPn26qr0B6ik3NCzbX43Ph5SjHxm4eqObGYENERIYnE+XseU5ycjJUKhWSkpLg7Oxs6nLKhdgUNT7cGIeoeDV8nPKCjb8rgw0REelPyv37he4w+blIJpO9yGnIgsWkqPHhj3G4kaCGr5MCG7u5owqDDRERGVGJOhSvXbsWDRo0gJ2dHezs7PDSSy9h3bp1hq6NzNy9ZDW6bsgLNpWcFYjozmBDRETGJ/lOM3/+fEyePBmffvopWrduDQA4evQoBg0ahLi4OIwYMcLgRZL5uZuciw9/fIToRDUqqfJabPxUDDZERGR8kvvcVK1aFdOmTUPv3r11tq9ZswZTp07FjRs3DFqgobHPjfHdScoLNreT1KjskhdsKjoz2BARUckZtc/N/fv30apVq0LbW7Vqhfv370s9HVmY6MRcdPvxEe4kq1HFRYGN3Tzg68xh+kREVHok97kJCAjApk2bCm2PiIhAjRo1DFIUmafoxLwWmzvJalR1VWBTdwYbIiIqfZJbbqZNm4aQkBD89ttv2j43x44dQ2RkZJGhh8qHmwm5+PDHONxP0aC6mxV+/NAdXk4MNkREVPokt9y8//77+P333+Hh4YEdO3Zgx44d8PDwwKlTp/Duu+8ao0Yq427E5yJkQ4Fg043BhoiITIeT+NELuf4oFx9ujMODVA1quOcFmwoODDZERGRYUu7fkltuAgMDsXr1aiQnJ5e4QLIMV+NyEPJjXrCp5WGFjQw2RERUBkgON/Xq1cP48ePh7e2NDz74AP/73/+Qk5NjjNqoDLsSl4NuGx/hYZoGdSrktdh4MNgQEVEZIDncLFy4EHfv3sWOHTvg4OCA3r17w8vLCwMHDsThw4eNUSOVMZcf5uDDH/OCTV1PK2z40B3u9gw2RERUNrxwn5vMzEzs2rUL06dPx/nz56FWqw1Vm1Gwz82LufggB903PkJ8hgb1vKyxPsQdrnYlWsWDiIhIb6W2cGZMTAw2btyIH374AX///TeaN2/+IqejMu7f2Bz0iHiEhAwNGnhb44eu7nBhsCEiojJG8p0pOTkZq1atQrt27eDn54dvv/0WHTt2xNWrV3Hy5Elj1EhlwPmYbHTfGIeEDA0a+uS12DDYEBFRWSS55cbLywuurq4ICQnBzJkz0axZM2PURWVIXrB5hOQsgcY+1ljT1R0qWwYbIiIqmySHm507d+LNN9+EXM6bW3nw1/1s9Ix4HGx8rbG2qzuclfyzJyKisktyuGnXrp0x6qAy6Oy9bPSKeISUbIGmFa2x5gN3ODHYEBFRGadXuGnSpAkiIyPh6uqKxo0bQyaTFbvvmTNnDFYcmc7pu9novekRUrMFXq5kg9Vd3ODIYENERGZAr3DTqVMnKJVK7c/PCjdk/v64k4XQzfFIyxZo4WeDVV3c4GDDYENEROaBa0uRjoLBpmVlG6x83w32DDZERGRiRl1bqlq1anj06FGh7YmJiahWrZrU01EZ8vvtLPTelBdsWlfJa7FhsCEiInMjuUPxzZs3i5yFOCsrC3fu3DFIUVT6TkRnoe+WeGTkCLTxV+L799xga83Hj0REZH70Djc7d+7U/vzzzz9DpVJpf1er1YiMjETVqlUNWx2ViqM3s9BvazwycwXaVlVi2bsMNkREZL70DjedO3cGAMhkMoSGhuq8Z21tDX9/f8ybN8+gxZHxHbmRiX7b4pGVC7xRTYml77rB1orBhoiIzJfe4Uaj0QAAqlatij/++AMeHh5GK4pKx+GoTAzYFo8sNfBmdSW+7ewGJYMNERGZOcl9bm7cuGGMOqiU/Xo9Ex9vzws27QJssaSTK4MNERFZBL3Czddff42BAwfC1tYWX3/99TP3HTZsmEEKI+OJvJaJQTvika0GgmrYYnEnV9goGGyIiMgy6DXPTdWqVfHnn3/C3d39mZ2GZTIZoqKiDFqgoZX3eW72X83EJzvikaMB3q5pi8UdXWHNYENERGWclPu3Xi03BR9F8bGU+fr5SgaG/C8BORqgQy1bLAxmsCEiIsvzwjO0qdVqnDt3DgkJCYaoh4xk7+UMDH4cbIJr2+JrttgQEZGFkhxuPvvsM6xYsQJAXrB57bXX0KRJE/j5+eHQoUOGro8MYM+lDHz6vwTkaoBOde2wINgVVnIGGyIiskySw82WLVvQsGFDAMCuXbtw8+ZNXLp0CSNGjMDEiRMNXiC9mF0XMzB0ZwLUAni3nh2+6uDCYENERBZNcriJi4uDt7c3AGDv3r344IMPULNmTXz00Uc4f/68wQukkttxIR3DduUFmy717TCvvQsUDDZERGThJIcbLy8vXLhwAWq1Gvv27UO7du0AAOnp6VAoFAYvkEpm27/pGLE7ERoBfNDADnPeYbAhIqLyQfIkfn379kXXrl3h4+MDmUyGwMBAAMDvv/+O2rVrG7xAkm7L+XSM2psIAeDDl+wx820V5DIGGyIiKh8kh5upU6eifv36uH37Nj744AMolUoAgEKhwLhx4wxeIEkT8Xc6xv6UF2x6NLLHl28x2BARUfmi1yR+lsSSJ/H78a80jNuXBADo2dgeX7RjsCEiIssg5f5donluDh8+jODgYAQEBCAgIAAdO3bEkSNHSlQsGUZ0Yi4m/JwXbEKbOOBLBhsiIiqnJIebH374AYGBgbC3t8ewYcMwbNgw2NnZ4c0338SGDRuMUSPp4XxMDjQCqOdljWmBzpAx2BARUTkluc/N9OnTMWfOHIwYMUK7bdiwYZg/fz6++OILdO/e3aAFkn6ux+cCAOpUsGKwISKick1yy01UVBSCg4MLbe/YsSPXnTKh64/ywk01N8l5lYiIyKJIDjd+fn6IjIwstP3AgQPw8/MzSFEkXVQ8ww0RERFQgsdSI0eOxLBhw3Du3Dm0atUKAHDs2DGsXr0aCxcuNHiB9HxCCG24qc5wQ0RE5ZzkO+Enn3wCb29vzJs3D5s2bQIA1KlTBxEREejUqZPBC6Tne5CmQWq2gFwGVHFluCEiovJN0p1QCIFr166hZs2aOHToEKyseCMtC/JbbfxUCiit2JmYiIjKN7373Ny4cQMvvfQSateujZdeegnVq1fHn3/+aczaSE/sb0NERPSE3uFm9OjRyM3NxQ8//IAtW7agUqVKGDhwoDFrIz1xpBQREdETet8Njx49ii1btuDVV18FALzyyiuoVKkS0tLS4ODgYLQC6fny57ip7s5wQ0REpHfLzYMHD1CjRg3t7z4+PrCzs8ODBw+MUhjpj4+liIiIntD7biiTyZCamgo7OzvtNrlcjpSUFCQnJ2u3WdpilGVdVq7AnSQ1AIYbIiIiQEK4EUKgZs2ahbY1btxY+7NMJoNarTZshfRMtxJyoRGAk40Mng4lWgeViIjIougdbn799Vdj1kEllN/fppo715QiIiICJISbtm3bGrMOKiH2tyEiItLF5xhm7jrDDRERkQ6GGzMX9YhrShERERVk8nCzZMkS+Pv7w9bWFi1atMCpU6eeuX9iYiKGDBkCHx8fKJVK1KxZE3v37i2lassWIQRbboiIiJ5i0jtiREQEwsLCsHTpUrRo0QILFixAUFAQLl++DE9Pz0L7Z2dno127dvD09MSWLVtQsWJF3Lp1Cy4uLqVffBnwKF2D5CwBGYCqXDCTiIgIwAuGmzt37gAAKlWqVKLj58+fjwEDBqBv374AgKVLl2LPnj1YuXIlxo0bV2j/lStXIj4+HsePH4e1tTUAwN/fv2TFW4D8zsQVVQrYWnOkFBEREVCCx1IajQaff/45VCoVqlSpgipVqsDFxQVffPEFNBqN3ufJzs7G6dOnERgY+KQYuRyBgYE4ceJEkcfs3LkTLVu2xJAhQ+Dl5YX69etjxowZz5xbJysrC8nJyTovS6FddoGPpIiIiLQk3xUnTpyIFStWYNasWWjdujWAvHWnpk6diszMTEyfPl2v88TFxUGtVsPLy0tnu5eXFy5dulTkMVFRUTh48CB69OiBvXv34tq1axg8eDBycnIQHh5e5DEzZ87EtGnTJHxD88EFM4mIiAqTfFdcs2YNvv/+e3Ts2FG77aWXXkLFihUxePBgvcNNSWg0Gnh6emLZsmVQKBRo2rQp7t69i//+97/Fhpvx48cjLCxM+3tycjL8/PyMVmNp4hw3REREhUm+K8bHx6N27dqFtteuXRvx8fF6n8fDwwMKhQKxsbE622NjY+Ht7V3kMT4+PrC2toZCodBuq1OnDmJiYpCdnQ0bG5tCxyiVSiiVSr3rMicMN0RERIVJ7nPTsGFDLF68uND2xYsXo2HDhnqfx8bGBk2bNkVkZKR2m0ajQWRkJFq2bFnkMa1bt8a1a9d0+vZcuXIFPj4+RQYbS5atFohOzOtrFODOcENERJRP8l1xzpw56NChAw4cOKANISdOnMDt27clzzcTFhaG0NBQNGvWDM2bN8eCBQuQlpamHT3Vu3dvVKxYETNnzgQAfPLJJ1i8eDGGDx+OoUOH4urVq5gxYwaGDRsm9WuYvejEXKgF4GAjg5ejyacrIiIiKjMkh5u2bdviypUrWLJkibbj73vvvYfBgwfD19dX0rlCQkLw8OFDTJkyBTExMWjUqBH27dun7WQcHR0NufzJjdvPzw8///wzRowYoe3nM3z4cIwdO1bq1zB7+Y+kqrpywUwiIqKCZEIIoe/OOTk5ePvtt7F06VLUqFHDmHUZTXJyMlQqFZKSkuDs7Gzqckrs25MpmHU4BR3r2GFRR1dTl0NERGRUUu7fkp5nWFtb4++//36h4sgwOMcNERFR0SR31ujZsydWrFhhjFpIAo6UIiIiKprkO2Nubi5WrlyJAwcOoGnTpnBwcNB5f/78+QYrjooXFZ83Uqo6R0oRERHpkHxn/Oeff9CkSRMAecOwC2LH1tKRkKFBQkbecPiqrorn7E1ERFS+SA43v/76qzHqIAnyl13wdVLA3obDwImIiArindEMPelvw1YbIiKip+nVcvPee+9h9erVcHZ2xnvvvffMfbdt22aQwqh4+SOlqrG/DRERUSF63R1VKpW2P41KpTJqQfR8HClFRERUPL3ujqtWrSryZzKN/D43nOOGiIiosBL1ucnNzcWBAwfw3XffISUlBQBw7949pKamGrQ4KixXIxCd+Djc8LEUERFRIZLvjrdu3cLbb7+N6OhoZGVloV27dnBycsLs2bORlZWFpUuXGqNOeux2oho5GsDWSgYfJ3YoJiIieprklpvhw4ejWbNmSEhIgJ2dnXb7u+++i8jISIMWR4UVHCkl57xCREREhUhuuTly5AiOHz8OGxsbne3+/v64e/euwQqjol17xM7EREREzyK55Uaj0UCtVhfafufOHTg5ORmkKCoeR0oRERE9m+Rw89Zbb2HBggXa32UyGVJTUxEeHo727dsbsjYqAsMNERHRs0m+Q86bNw9BQUGoW7cuMjMz0b17d1y9ehUeHh748ccfjVEjFZAfbgI4UoqIiKhIku+QlSpVwl9//YWNGzfi77//RmpqKvr164cePXrodDAmw0vK1CAuPX/BTIYbIiKiopToDmllZYWePXsauhZ6jvxWGy9HORyVXBaMiIioKHqFm507d+p9wo4dO5a4GHq26xwpRURE9Fx63SU7d+6s87tMJoMQotA2AEWOpCLDyG+54bILRERExdPr2YZGo9G+fvnlFzRq1Ag//fQTEhMTkZiYiJ9++glNmjTBvn37jF1vuXadI6WIiIieS/Jd8rPPPsPSpUvx6quvarcFBQXB3t4eAwcOxMWLFw1aID2hbbnhSCkiIqJiSe6Vev36dbi4uBTarlKpcPPmTQOUREVRawRuJbDlhoiI6Hkkh5uXX34ZYWFhiI2N1W6LjY3F6NGj0bx5c4MWR0/cTVYjSw0oFUBFZy6YSUREVBzJ4WblypW4f/8+KleujICAAAQEBKBy5cq4e/cuVqxYYYwaCU9GSvm7WkEh54KZRERExZH8fCMgIAB///039u/fj0uXLgEA6tSpg8DAQO2IKTI8diYmIiLST4nulDKZDG+99RbeeustQ9dDxdCuKcXOxERERM9UojtlWloaDh8+jOjoaGRnZ+u8N2zYMIMURrryW24C2HJDRET0TJLvlGfPnkX79u2Rnp6OtLQ0uLm5IS4uDvb29vD09GS4MRKuBk5ERKQfyR2KR4wYgeDgYCQkJMDOzg4nT57ErVu30LRpU8ydO9cYNZZ7KVkaPEjNWzCTj6WIiIieTXK4OXfuHEaOHAm5XA6FQoGsrCz4+flhzpw5mDBhgjFqLPduPG61qeAghzMXzCQiInomyXdKa2tryOV5h3l6eiI6OhpA3iR+t2/fNmx1BAC4xkdSREREepN8t2zcuDH++OMP1KhRA23btsWUKVMQFxeHdevWoX79+saosdyL4mrgREREepPccjNjxgz4+PgAAKZPnw5XV1d88sknePjwIZYtW2bwAomdiYmIiKSQfLds1qyZ9mdPT0+uBF4KuGAmERGR/tg7tYzTCIEbCWoAQHW23BARET2XXnfLxo0b6720wpkzZ16oINJ1L1mNzFwBazlQScUFM4mIiJ5Hr3DTuXNn7c+ZmZn45ptvULduXbRs2RIAcPLkSfz7778YPHiwUYosz/IXzKziagUrLphJRET0XHqFm/DwcO3P/fv3x7Bhw/DFF18U2odDwQ1P29+Gj6SIiIj0IrnPzebNm9G7d+9C23v27ImtW7capCh6gquBExERSSM53NjZ2eHYsWOFth87dgy2trYGKYqe4EgpIiIiaSTfMT/77DN88sknOHPmDJo3bw4A+P3337Fy5UpMnjzZ4AWWd5zjhoiISBrJd8xx48ahWrVqWLhwIX744QcAQJ06dbBq1Sp07drV4AWWZ+nZGtxPyVswk31uiIiI9CPpjpmbm4sZM2bgo48+YpApBVGP57dxs5PDxY5TEhEREelD0h3TysoKc+bMQW5urrHqoQKuP8oBwEdSREREUkhuDnjzzTdx+PBhY9RCT2F/GyIiIukk3zXfeecdjBs3DufPn0fTpk3h4OCg837Hjh0NVlx5FxX/eNkFjpQiIiLSm+S7Zv4sxPPnzy/0nkwmg1qtfvGqCMCTOW7YmZiIiEh/ku+aGo3GGHXQU4QQuMHHUkRERJK90BCczMxMQ9VBT4lJ0SA9R8BKDlR24YKZRERE+pIcbtRqNb744gtUrFgRjo6OiIqKAgBMnjwZK1asMHiB5VX+I6nKLgpYK7hgJhERkb4kh5vp06dj9erVmDNnDmxsbLTb69evj++//96gxZVnXFOKiIioZCSHm7Vr12LZsmXo0aMHFIonj0saNmyIS5cuGbS48oyrgRMREZWM5HBz9+5dBAQEFNqu0WiQk5NjkKIIiHrElhsiIqKSkBxu6tatiyNHjhTavmXLFjRu3NggRRFXAyciIiopyXfOKVOmIDQ0FHfv3oVGo8G2bdtw+fJlrF27Frt37zZGjeVORo4Gd5Pz5gtiyw0REZE0erfcxMfHAwA6deqEXbt24cCBA3BwcMCUKVNw8eJF7Nq1C+3atStREUuWLIG/vz9sbW3RokULnDp1Sq/jNm7cCJlMhs6dO5foc8uqGwlqCAAqWxncuGAmERGRJHo3C/j6+qJz587o168f2rVrh/379xukgIiICISFhWHp0qVo0aIFFixYgKCgIFy+fBmenp7FHnfz5k2MGjUKbdq0MUgdZUnB/jYyGYeBExERSaF3s8Dy5cvx8OFDvP322/D398fUqVNx69atFy5g/vz5GDBgAPr27Yu6deti6dKlsLe3x8qVK4s9Rq1Wo0ePHpg2bRqqVav2wjWUNVx2gYiIqOT0Dje9evVCZGQkrl27htDQUKxZswbVq1dHu3btEBERgezsbMkfnp2djdOnTyMwMPBJQXI5AgMDceLEiWKP+/zzz+Hp6Yl+/fpJ/kxzwM7EREREJSe5Q0fVqlUxbdo03LhxA/v27YOnpyc++ugj+Pj4YNiwYZLOFRcXB7VaDS8vL53tXl5eiImJKfKYo0ePYsWKFVi+fLlen5GVlYXk5GSdV1kXxQn8iIiISuyFeqsGBgZi/fr1WLt2LYC8jsHGlJKSgl69emH58uXw8PDQ65iZM2dCpVJpX35+fkat8UUJIRhuiIiIXkCJ7563bt3CqlWrsGbNGty+fRtvvPGG5MdEHh4eUCgUiI2N1dkeGxsLb2/vQvtfv34dN2/eRHBwsHZb/irlVlZWuHz5MqpXr65zzPjx4xEWFqb9PTk5uUwHnAdpGqRmC8hlQBUXhhsiIiKpJN09s7KysHXrVqxcuRKHDh1CxYoV0adPH/Tt2xf+/v6SP9zGxgZNmzZFZGSkdji3RqNBZGQkPv3000L7165dG+fPn9fZNmnSJKSkpGDhwoVFhhalUgmlUim5NlO5/niklJ9KAaUVR0oRERFJpXe4GTx4MDZu3Ij09HR06tQJe/fuRbt27V54qHJYWBhCQ0PRrFkzNG/eHAsWLEBaWhr69u0LAOjduzcqVqyImTNnwtbWFvXr19c53sXFBQAKbTdXfCRFRET0YvS+gx49ehTh4eHo2bMn3N3dDVZASEgIHj58iClTpiAmJgaNGjXCvn37tJ2Mo6OjIZeXn4nsOFKKiIjoxciEEMLURZSm5ORkqFQqJCUlwdnZ2dTlFBK6+REORWVhZpAK3Rs5mLocIiKiMkHK/bv8NImYCT6WIiIiejEMN2VIZq7AnSQumElERPQiGG7KkFsJudAIwMlGhgoO/KMhIiIqCd5By5D8NaWquXPBTCIiopIqUbg5cuQIevbsiZYtW+Lu3bsAgHXr1uHo0aMGLa68ieKCmURERC9McrjZunUrgoKCYGdnh7NnzyIrKwsAkJSUhBkzZhi8wPKEnYmJiIhenORw8+WXX2Lp0qVYvnw5rK2ttdtbt26NM2fOGLS48ibqEcMNERHRi5Icbi5fvozXXnut0HaVSoXExERD1FQuCSG0fW44gR8REVHJSQ433t7euHbtWqHtR48eRbVq1QxSVHkUl65BcpaADIA/F8wkIiIqMcnhZsCAARg+fDh+//13yGQy3Lt3D+vXr8eoUaPwySefGKPGciG/v01FlQK21hwpRUREVFKSmwjGjRsHjUaDN998E+np6XjttdegVCoxatQoDB061Bg1lgv5/W04UoqIiOjFSL6TymQyTJw4EaNHj8a1a9eQmpqKunXrwtHR0Rj1lRvXOQyciIjIIEp8J7WxsUHdunUNWUu5FlVgAj8iIiIqOb3upO+9957eJ9y2bVuJiynPOMcNERGRYejVoVilUmlfzs7OiIyMxJ9//ql9//Tp04iMjIRKpTJaoZYsWy0QnZi3YCYfSxEREb0Yve6kq1at0v48duxYdO3aFUuXLoVCoQAAqNVqDB48GM7Ozsap0sLdSsiFWgAONjJ4OXK5LyIiohch+U66cuVKjBo1ShtsAEChUCAsLAwrV640aHHlRf4jqaquXDCTiIjoRUkON7m5ubh06VKh7ZcuXYJGozFIUeVNFGcmJiIiMhjJd9O+ffuiX79+uH79Opo3bw4A+P333zFr1iz07dvX4AWWBxwGTkREZDiS76Zz586Ft7c35s2bh/v37wMAfHx8MHr0aIwcOdLgBZYHHClFRERkODIhhCjpwcnJyQBgVh2Jk5OToVKpkJSUVGbqbvR1DBIyNNjbpwLqeVk//wAiIqJyRsr9+4WaCspKODBn8elqJGTk9VWq6qp4zt5ERET0PBx3bGL5/W18nRSwt+EfBxER0Yvi3dTEouIfT97HkVJEREQGwXBjYuxMTEREZFgMNybGcENERGRYJbqjpqWl4fDhw4iOjkZ2drbOe8OGDTNIYeXF9Uf5c9ywMzEREZEhSA43Z8+eRfv27ZGeno60tDS4ubkhLi4O9vb28PT0ZLiRIEctcCvxccsN+9wQEREZhOTHUiNGjEBwcDASEhJgZ2eHkydP4tatW2jatCnmzp1rjBot1u0kNXI1gK2VDD5ObLkhIiIyBMnh5ty5cxg5ciTkcjkUCgWysrLg5+eHOXPmYMKECcao0WI96W+jgJwLZhIRERmE5HBjbW0NuTzvME9PT0RHRwMAVCoVbt++bdjqLNyT/jZ8JEVERGQoku+qjRs3xh9//IEaNWqgbdu2mDJlCuLi4rBu3TrUr1/fGDVaLI6UIiIiMjzJLTczZsyAj48PAGD69OlwdXXFJ598gocPH+K7774zeIGWTBtu2JmYiIjIYCTfVZs1a6b92dPTE/v27TNoQeVJ/tILfCxFRERkOJJbbi5dulTsez///PMLFVOeJGVq8Cg9f8FMhhsiIiJDkRxumjRpgiVLluhsy8rKwqeffopOnToZrDBLl9+Z2NtRDkclJ4omIiIyFMl31dWrV2PKlClo3749YmNjce7cOTRu3BgHDhzAkSNHjFGjRWJ/GyIiIuOQHG66du2Kv/76Czk5OahXrx5atmyJtm3b4syZM3j55ZeNUaNF4kgpIiIi4yjx85Ds7Gyo1Wqo1Wr4+PjA1tbWkHVZPHYmJiIiMg7J4Wbjxo1o0KABVCoVrly5gj179mDZsmVo06YNoqKijFGjRWLLDRERkXFIDjf9+vXDjBkzsHPnTlSoUAHt2rXD+fPnUbFiRTRq1MgIJVoetUbgZgLDDRERkTFIvrOeOXMGtWrV0tnm6uqKTZs2Yd26dQYrzJLdSVIjWw0oFUBFZy6YSUREZEiSW26eDjYF9erV64WKKS/y+9tUdbOCQs4FM4mIiAypRM9E7ty5g507dyI6OhrZ2dk6782fP98ghVky9rchIiIyHsl318jISHTs2BHVqlXDpUuXUL9+fdy8eRNCCDRp0sQYNVochhsiIiLjkfxYavz48Rg1ahTOnz8PW1tbbN26Fbdv30bbtm3xwQcfGKNGi8Nh4ERERMYjOdxcvHgRvXv3BgBYWVkhIyMDjo6O+PzzzzF79myDF2iJ8pdeYMsNERGR4UkONw4ODtp+Nj4+Prh+/br2vbi4OMNVZqFSsjR4mJa3YCaXXiAiIjI8vcPN559/jrS0NLzyyis4evQoAKB9+/YYOXIkpk+fjo8++givvPKK0Qq1FPn9bSo4yOHMBTOJiIgMTu+767Rp05CWlob58+ejRYsW2m1vvvkmIiIi4O/vjxUrVhitUEvB/jZERETGpfcdVggBAKhWrZp2m4ODA5YuXWr4qixYFPvbEBERGZWk5yIyGSece1EcBk5ERGRcku6wNWvWfG7AiY+Pf6GCLF3+Yyl2JiYiIjIOSXfYadOmQaVSGasWi6cRAjcS2OeGiIjImCTdYT/88EN4enoaqxaLdzdZjaxcwEYBVFJxwUwiIiJj0LvPDfvbvLj8zsRVXKxgxQUziYiIjELvcJM/WsoYlixZAn9/f9ja2qJFixY4depUsfsuX74cbdq0gaurK1xdXREYGPjM/cuSKPa3ISIiMjq9w41GozHKI6mIiAiEhYUhPDwcZ86cQcOGDREUFIQHDx4Uuf+hQ4fQrVs3/Prrrzhx4gT8/Pzw1ltv4e7duwavzdA4xw0REZHxyYQxm2T00KJFC7z88stYvHgxgLwQ5efnh6FDh2LcuHHPPV6tVsPV1RWLFy/Wrnn1LMnJyVCpVEhKSoKzs/ML1y9F941xOHYrG3Pbu+CDBval+tlERETmTMr926Tz/2dnZ+P06dMIDAzUbpPL5QgMDMSJEyf0Okd6ejpycnLg5uZmrDINhgtmEhERGZ9J77JxcXFQq9Xw8vLS2e7l5YVLly7pdY6xY8fC19dXJyAVlJWVhaysLO3vycnJJS/4BaRlaxCTmrdgJh9LERERGY9Zr9w4a9YsbNy4Edu3b4etrW2R+8ycORMqlUr78vPzK+Uq8+R3Jna3l8PFzqwvOxERUZlm0rush4cHFAoFYmNjdbbHxsbC29v7mcfOnTsXs2bNwi+//IKXXnqp2P3Gjx+PpKQk7ev27dsGqV0qLrtARERUOkwabmxsbNC0aVNERkZqt2k0GkRGRqJly5bFHjdnzhx88cUX2LdvH5o1a/bMz1AqlXB2dtZ5mQLDDRERUekw+Z02LCwMoaGhaNasGZo3b44FCxYgLS0Nffv2BQD07t0bFStWxMyZMwEAs2fPxpQpU7Bhwwb4+/sjJiYGAODo6AhHR0eTfY/niYpXA2C4ISIiMjaT32lDQkLw8OFDTJkyBTExMWjUqBH27dun7WQcHR0NufxJA9O3336L7OxsdOnSRec84eHhmDp1ammWLsm1RzkA2JmYiIjI2Ew+z01pM8U8NxohUPerGGTkCBzs74nqnKGYiIhIErOZ56a8iEnRICNHwEoOVHbhgplERETGxHBTCvI7E1dxsYK1ggtmEhERGRPDTSm4zpFSREREpYbhphQ8GQbOR1JERETGxnBTCrimFBERUelhuCkF+S03HCVFRERkfAw3RpaRo8Hd5LwJ/DjHDRERkfEx3BjZjYS8YONiK4ObPfvcEBERGRvDjZFFsb8NERFRqWK4MbL8YeB8JEVERFQ6GG6MTDsMnJ2JiYiISgXDjZFxAj8iIqLSxXBjREKIJ8PAGW6IiIhKBcONET1I1SAtW0AhA6q4MtwQERGVBoYbI8p/JOXnooANF8wkIiIqFQw3RhTF/jZERESljuHGiBhuiIiISh/DjRFde8TOxERERKWN4caIuGAmERFR6WO4MZLMXIE7SXnrSvGxFBERUelhuDGSWwm5EACclTJ42PMyExERlRbedY2k4MzEMhmHgRMREZUWhhsj4UgpIiIi02C4MZLrjxhuiIiITIHhxkjYckNERGQaDDdGUHDBzAAOAyciIipVDDdGEJeuQXKWgAxcMJOIiKi0MdwYQX6rTSWVArZWHClFRERUmhhujCCKnYmJiIhMhuHGCPLnuOGaUkRERKWP4cYItBP4sTMxERFRqWO4MYIottwQERGZDMONgWWrBW4n5i2YyXBDRERU+hhuDOxWQi7UAnCwkcHTkZeXiIiotPHua2BRXDCTiIjIpBhuDIzLLhAREZkWw42B5S+Yyf42REREpsFwY2Cc44aIiMi0GG4MLIpz3BAREZkUw40BxaerkZgpAABVXRUmroaIiKh8YrgxoPxHUhWdFbCz5qUlIiIyBd6BDSgqPm/yPo6UIiIiMh2GGwO6/igHAMMNERGRKTHcGFB+y011diYmIiIyGYYbA+KCmURERKbHcGMgOWqBW4mcnZiIiMjUGG4M5HaSGrkawM5aBm8nXlYiIiJT4V3YQBIzNAAAD3s55Fwwk4iIyGQYbgyMuYaIiMi0GG6IiIjIojDcEBERkUVhuCEiIiKLwnBDREREFoXhhoiIiCwKww0RERFZFIYbIiIisigMN0RERGRRykS4WbJkCfz9/WFra4sWLVrg1KlTz9x/8+bNqF27NmxtbdGgQQPs3bu3lColIiKiss7k4SYiIgJhYWEIDw/HmTNn0LBhQwQFBeHBgwdF7n/8+HF069YN/fr1w9mzZ9G5c2d07twZ//zzTylXTkRERGWRTAghTFlAixYt8PLLL2Px4sUAAI1GAz8/PwwdOhTjxo0rtH9ISAjS0tKwe/du7bZXXnkFjRo1wtKlS5/7ecnJyVCpVEhKSoKzs7PBvseZu9l494c4VHZR4MjHXgY7LxEREUm7f5u05SY7OxunT59GYGCgdptcLkdgYCBOnDhR5DEnTpzQ2R8AgoKCit0/KysLycnJOi8iIiKyXCYNN3FxcVCr1fDy0m3p8PLyQkxMTJHHxMTESNp/5syZUKlU2pefn59hin+KTAYorQAbBVfOJCIiMiWT97kxtvHjxyMpKUn7un37tlE+p7GvDa6M9EVkf0+jnJ+IiIj0Y2XKD/fw8IBCoUBsbKzO9tjYWHh7exd5jLe3t6T9lUollEqlYQomIiKiMs+kLTc2NjZo2rQpIiMjtds0Gg0iIyPRsmXLIo9p2bKlzv4AsH///mL3JyIiovLFpC03ABAWFobQ0FA0a9YMzZs3x4IFC5CWloa+ffsCAHr37o2KFSti5syZAIDhw4ejbdu2mDdvHjp06ICNGzfizz//xLJly0z5NYiIiKiMMHm4CQkJwcOHDzFlyhTExMSgUaNG2Ldvn7bTcHR0NOTyJw1MrVq1woYNGzBp0iRMmDABNWrUwI4dO1C/fn1TfQUiIiIqQ0w+z01pM9Y8N0RERGQ8ZjPPDREREZGhMdwQERGRRWG4ISIiIovCcENEREQWheGGiIiILArDDREREVkUhhsiIiKyKAw3REREZFEYboiIiMiimHz5hdKWPyFzcnKyiSshIiIifeXft/VZWKHchZuUlBQAgJ+fn4krISIiIqlSUlKgUqmeuU+5W1tKo9Hg3r17cHJygkwmM+i5k5OT4efnh9u3b3PdKiPidS4dvM6lg9e59PBalw5jXWchBFJSUuDr66uzoHZRyl3LjVwuR6VKlYz6Gc7OzvwPpxTwOpcOXufSwetcenitS4cxrvPzWmzysUMxERERWRSGGyIiIrIoDDcGpFQqER4eDqVSaepSLBqvc+ngdS4dvM6lh9e6dJSF61zuOhQTERGRZWPLDREREVkUhhsiIiKyKAw3REREZFEYboiIiMiiMNxItGTJEvj7+8PW1hYtWrTAqVOnnrn/5s2bUbt2bdja2qJBgwbYu3dvKVVq3qRc5+XLl6NNmzZwdXWFq6srAgMDn/vnQnmk/n3Ot3HjRshkMnTu3Nm4BVoIqdc5MTERQ4YMgY+PD5RKJWrWrMl/O/Qg9TovWLAAtWrVgp2dHfz8/DBixAhkZmaWUrXm6bfffkNwcDB8fX0hk8mwY8eO5x5z6NAhNGnSBEqlEgEBAVi9erXR64QgvW3cuFHY2NiIlStXin///VcMGDBAuLi4iNjY2CL3P3bsmFAoFGLOnDniwoULYtKkScLa2lqcP3++lCs3L1Kvc/fu3cWSJUvE2bNnxcWLF0WfPn2ESqUSd+7cKeXKzYvU65zvxo0bomLFiqJNmzaiU6dOpVOsGZN6nbOyskSzZs1E+/btxdGjR8WNGzfEoUOHxLlz50q5cvMi9TqvX79eKJVKsX79enHjxg3x888/Cx8fHzFixIhSrty87N27V0ycOFFs27ZNABDbt29/5v5RUVHC3t5ehIWFiQsXLohFixYJhUIh9u3bZ9Q6GW4kaN68uRgyZIj2d7VaLXx9fcXMmTOL3L9r166iQ4cOOttatGghPv74Y6PWae6kXuen5ebmCicnJ7FmzRpjlWgRSnKdc3NzRatWrcT3338vQkNDGW70IPU6f/vtt6JatWoiOzu7tEq0CFKv85AhQ8T//d//6WwLCwsTrVu3NmqdlkSfcDNmzBhRr149nW0hISEiKCjIiJUJwcdSesrOzsbp06cRGBio3SaXyxEYGIgTJ04UecyJEyd09geAoKCgYvenkl3np6WnpyMnJwdubm7GKtPslfQ6f/755/D09ES/fv1Ko0yzV5LrvHPnTrRs2RJDhgyBl5cX6tevjxkzZkCtVpdW2WanJNe5VatWOH36tPbRVVRUFPbu3Yv27duXSs3lhanug+Vu4cySiouLg1qthpeXl852Ly8vXLp0qchjYmJiitw/JibGaHWau5Jc56eNHTsWvr6+hf6DoidKcp2PHj2KFStW4Ny5c6VQoWUoyXWOiorCwYMH0aNHD+zduxfXrl3D4MGDkZOTg/Dw8NIo2+yU5Dp3794dcXFxePXVVyGEQG5uLgYNGoQJEyaURsnlRnH3weTkZGRkZMDOzs4on8uWG7Ios2bNwsaNG7F9+3bY2tqauhyLkZKSgl69emH58uXw8PAwdTkWTaPRwNPTE8uWLUPTpk0REhKCiRMnYunSpaYuzaIcOnQIM2bMwDfffIMzZ85g27Zt2LNnD7744gtTl0YGwJYbPXl4eEChUCA2NlZne2xsLLy9vYs8xtvbW9L+VLLrnG/u3LmYNWsWDhw4gJdeesmYZZo9qdf5+vXruHnzJoKDg7XbNBoNAMDKygqXL19G9erVjVu0GSrJ32cfHx9YW1tDoVBot9WpUwcxMTHIzs6GjY2NUWs2RyW5zpMnT0avXr3Qv39/AECDBg2QlpaGgQMHYuLEiZDL+f/9DaG4+6Czs7PRWm0AttzozcbGBk2bNkVkZKR2m0ajQWRkJFq2bFnkMS1bttTZHwD2799f7P5UsusMAHPmzMEXX3yBffv2oVmzZqVRqlmTep1r166N8+fP49y5c9pXx44d8cYbb+DcuXPw8/MrzfLNRkn+Prdu3RrXrl3ThkcAuHLlCnx8fBhsilGS65yenl4owOQHSsElFw3GZPdBo3ZXtjAbN24USqVSrF69Wly4cEEMHDhQuLi4iJiYGCGEEL169RLjxo3T7n/s2DFhZWUl5s6dKy5evCjCw8M5FFwPUq/zrFmzhI2NjdiyZYu4f/++9pWSkmKqr2AWpF7np3G0lH6kXufo6Gjh5OQkPv30U3H58mWxe/du4enpKb788ktTfQWzIPU6h4eHCycnJ/Hjjz+KqKgo8csvv4jq1auLrl27muormIWUlBRx9uxZcfbsWQFAzJ8/X5w9e1bcunVLCCHEuHHjRK9evbT75w8FHz16tLh48aJYsmQJh4KXRYsWLRKVK1cWNjY2onnz5uLkyZPa99q2bStCQ0N19t+0aZOoWbOmsLGxEfXq1RN79uwp5YrNk5TrXKVKFQGg0Cs8PLz0CzczUv8+F8Rwoz+p1/n48eOiRYsWQqlUimrVqonp06eL3NzcUq7a/Ei5zjk5OWLq1KmievXqwtbWVvj5+YnBgweLhISE0i/cjPz6669F/nubf21DQ0NF27ZtCx3TqFEjYWNjI6pVqyZWrVpl9DplQrD9jYiIiCwH+9wQERGRRWG4ISIiIovCcENEREQWheGGiIiILArDDREREVkUhhsiIiKyKAw3REREZFEYbojIYPr06YPOnTub7POnTp2KRo0amezzX8TNmzchk8meu+r666+/js8++6xUaiIyVww3RGZAJpM98zV16lRTl2gwr7/+epHfMTc319SlYerUqdp6rKys4O/vjxEjRiA1NfWFz+3n54f79++jfv36APJWrZbJZEhMTNTZb9u2bVy5mug5uCo4kRm4f/++9ueIiAhMmTIFly9f1m5zdHQ0RVlGM2DAAHz++ec626ysysY/V/Xq1cOBAweQm5uLY8eO4aOPPkJ6ejq+++67FzqvQqEodgXrgtzc3F7oc4jKA7bcEJkBb29v7UulUkEmk2l/T0tLQ48ePeDl5QVHR0e8/PLLOHDggPbYS5cuwd7eHhs2bNBu27RpE+zs7HDhwgUAwB9//IF27drBw8MDKpUKbdu2xZkzZ55Zk1qtRlhYGFxcXODu7o4xY8YUWk1Zo9Fg5syZqFq1Kuzs7NCwYUNs2bLlud/X3t5e5zvn3/THjh2LmjVrwt7eHtWqVcPkyZORk5NT7HkOHTqE5s2bw8HBAS4uLmjdujVu3bqlff/bb79F9erVYWNjg1q1amHdunXPrc3Kygre3t6oVKkSQkJC0KNHD+zcuRMAkJWVhWHDhsHT0xO2trZ49dVX8ccff2iPTUhIQI8ePVChQgXY2dmhRo0aWLVqFQDdx1I3b97EG2+8AQBwdXWFTCZDnz59AOg+lpowYQJatGhRqMaGDRtqw6FGo8Hnn3+OSpUqQalUolGjRti3b99zvyeROWO4ITJzqampaN++PSIjI3H27Fm8/fbbCA4ORnR0NACgdu3amDt3LgYPHozo6GjcuXMHgwYNwuzZs1G3bl0AQEpKCkJDQ3H06FGcPHkSNWrUQPv27ZGSklLs586bNw+rV6/GypUrcfToUcTHx2P79u06+8ycORNr167F0qVL8e+//2LEiBHo2bMnDh8+XKLv6uTkhNWrV+PChQtYuHAhli9fjq+++qrIfXNzc9G5c2e0bdsWf//9N06cOIGBAwdCJpMBALZv347hw4dj5MiR+Oeff/Dxxx+jb9+++PXXXyXVZGdnh+zsbADAmDFjsHXrVqxZswZnzpxBQEAAgoKCEB8fDwCYPHkyLly4gJ9++gkXL17Et99+Cw8Pj0Ln9PPzw9atWwEAly9fxv3797Fw4cJC+/Xo0QOnTp3C9evXtdv+/fdf/P333+jevTsAYOHChZg3bx7mzp2Lv//+G0FBQejYsSOuXr0q6XsSmRWjL81JRAa1atUqoVKpnrlPvXr1xKJFi3S2dejQQbRp00a8+eab4q233hIajabY49VqtXBychK7du0qdh8fHx8xZ84c7e85OTmiUqVK2pXCMzMzhb29vTh+/LjOcf369RPdunUr9rxt27YV1tbWwsHBQfsKCwsrct///ve/omnTptrfw8PDRcOGDYUQQjx69EgAEIcOHSry2FatWokBAwbobPvggw9E+/bti62t4PmFEOLPP/8UHh4eokuXLiI1NVVYW1uL9evXa9/Pzs4Wvr6+2usUHBws+vbtW+S5b9y4IQCIs2fPCiGerL789CrVbdu2FcOHD9f+3rBhQ/H5559rfx8/frxo0aKF9ndfX18xffp0nXO8/PLLYvDgwcV+TyJzVzYeYhNRiaWmpmLq1KnYs2cP7t+/j9zcXGRkZGhbbvKtXLkSNWvWhFwux7///qttwQCA2NhYTJo0CYcOHcKDBw+gVquRnp5e6Bz5kpKScP/+fZ1HIlZWVmjWrJn20dS1a9eQnp6Odu3a6RybnZ2Nxo0bP/M79ejRAxMnTtT+7uLiAiCvv9HXX3+N69evIzU1Fbm5uXB2di7yHG5ubujTpw+CgoLQrl07BAYGomvXrvDx8QEAXLx4EQMHDtQ5pnXr1kW2kBR0/vx5ODo6Qq1WIzs7Gx06dMDixYtx/fp15OTkoHXr1tp9ra2t0bx5c1y8eBEA8Mknn+D999/HmTNn8NZbb6Fz585o1arVMz/veXr06IGVK1di8uTJEELgxx9/RFhYGAAgOTkZ9+7d06kp/3v+9ddfL/S5RGUZww2RmRs1ahT279+PuXPnIiAgAHZ2dujSpYv2UUm+v/76C2lpaZDL5bh//772Jg8AoaGhePToERYuXIgqVapAqVSiZcuWhc4hRf4Ioj179qBixYo67ymVymceq1KpEBAQoLPtxIkT6NGjB6ZNm4agoCCoVCps3LgR8+bNK/Y8q1atwrBhw7Bv3z5ERERg0qRJ2L9/P1555ZUSfiugVq1a2LlzJ6ysrODr6wsbGxsAeQHxed555x3cunULe/fuxf79+/Hmm29iyJAhmDt3bonr6datG8aOHYszZ84gIyMDt2/fRkhISInPR2QJ2OeGyMwdO3YMffr0wbvvvosGDRrA29sbN2/e1NknPj4effr0wcSJE9GnTx/06NEDGRkZOucYNmwY2rdvj3r16kGpVCIuLq7Yz1SpVPDx8cHvv/+u3Zabm4vTp09rf69bty6USiWio6MREBCg8/Lz85P8PY8fP44qVapg4sSJaNasGWrUqKHTObg4jRs3xvjx43H8+HHUr19f27G6Tp06OHbsmM6+x44d0/ZDKo6NjQ0CAgLg7++vDTYAtB2TC54zJycHf/zxh845K1SogNDQUPzwww9YsGABli1bVuznAHkdt5+lUqVKaNu2LdavX4/169ejXbt28PT0BAA4OzvD19e3RN+TyJyx5YbIzNWoUQPbtm1DcHAwZDIZJk+eDI1Go7PPoEGD4Ofnh0mTJiErKwuNGzfGqFGjsGTJEu051q1bh2bNmiE5ORmjR4+GnZ3dMz93+PDhmDVrFmrUqIHatWtj/vz5OnOyODk5YdSoURgxYgQ0Gg1effVVJCUl4dixY3B2dkZoaKjk7xkdHY2NGzfi5Zdfxp49ewp1YC7oxo0bWLZsGTp27AhfX19cvnwZV69eRe/evQEAo0ePRteuXdG4cWMEBgZi165d2LZtm85IMykcHBzwySefYPTo0XBzc0PlypUxZ84cpKeno1+/fgCAKVOmoGnTpqhXrx6ysrKwe/du1KlTp8jzValSBTKZDLt370b79u1hZ2dX7JD/Hj16IDw8HNnZ2YU6WI8ePRrh4eGoXr06GjVqhFWrVuHcuXNYv359ib4nkVkwdacfIpLm6Q7FN27cEG+88Yaws7MTfn5+YvHixTqdTtesWSMcHBzElStXtMf8/vvvwtraWuzdu1cIIcSZM2dEs2bNhK2trahRo4bYvHmzqFKlivjqq6+KrSMnJ0cMHz5cODs7CxcXFxEWFiZ69+6t7VAshBAajUYsWLBA1KpVS1hbW4sKFSqIoKAgcfjw4WLP+3SH2YJGjx4t3N3dhaOjowgJCRFfffWVzrUo2OE3JiZGdO7cWfj4+AgbGxtRpUoVMWXKFKFWq7X7f/PNN6JatWrC2tpa1KxZU6xdu7bYup4+f1EyMjLE0KFDhYeHh1AqlaJ169bi1KlT2ve/+OILUadOHWFnZyfc3NxEp06dRFRUlBCicIdiIYT4/PPPhbe3t5DJZCI0NLTY65OQkCCUSqWwt7cXKSkpOu+p1WoxdepUUbFiRWFtbS0aNmwofvrpp2d+TyJzJxPiqYkpiIiIiMwY+9wQERGRRWG4ISIiIovCcENEREQWheGGiIiILArDDREREVkUhhsiIiKyKAw3REREZFEYboiIiMiiMNwQERGRRWG4ISIiIovCcENEREQWheGGiIiILMr/A0Fm6uTuethLAAAAAElFTkSuQmCC\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Treinando XGBoost...\n", "<PERSON>cur<PERSON><PERSON> média (XGBoost - validação cruzada): 0.9299\n", "\n", "Avaliação do modelo XGBoost:\n", "Acurácia: 0.9206\n", "Precisão: 0.9182\n", "Recall: 0.9266\n", "F1-Score: 0.9224\n", "AUC-ROC: 0.9759\n", "<PERSON><PERSON>:\n", "[[1069  100]\n", " [  89 1123]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# abordagem de conjunto usando VotingClassifier para combinar todos modelos.\n", "voting_clf = VotingClassifier(estimators=[\n", "    ('lr', LogisticRegression(max_iter=1000)),\n", "    ('rf', RandomForestClassifier(n_estimators=100, random_state=42)),\n", "    ('xgb', XGBClassifier(random_state=42, eval_metric='logloss'))\n", "], voting='soft')\n", "\n", "train_and_evaluate(voting_clf, X_train, y_train, X_test, y_test, \"Voting Classifier\", results, trained_models)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "WdegLcCfqcwB", "outputId": "acd88553-e164-46cd-895f-d3fc746da3a1"}, "execution_count": 19, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Treinando Voting Classifier...\n", "Acurácia média (Voting Classifier - validação cruzada): 0.9401\n", "\n", "Avaliação do modelo Voting Classifier:\n", "Acurácia: 0.9286\n", "Precisão: 0.9270\n", "Recall: 0.9332\n", "F1-Score: 0.9301\n", "AUC-ROC: 0.9805\n", "<PERSON><PERSON>:\n", "[[1080   89]\n", " [  81 1131]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# an<PERSON><PERSON><PERSON> de p<PERSON>vras-chave associadas a fake news.\n", "print(\"\\nIdentificando palavras-chave associadas a fake news...\")\n", "fake_news_indices = y_test[y_test == 0].index\n", "fake_news_words = ' '.join(test_df.loc[fake_news_indices, 'Noticia_processed'])\n", "fake_news_word_freq = Counter(fake_news_words.split())\n", "\n", "print(\"Palavras mais associadas a fake news:\")\n", "print(fake_news_word_freq.most_common(10))\n", "\n", "# plota as pala<PERSON><PERSON> mais frequentes em fake news.\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(x=[count for word, count in fake_news_word_freq.most_common(10)],\n", "            y=[word for word, count in fake_news_word_freq.most_common(10)])\n", "plt.title('Top 10 palavras associadas a fake news')\n", "plt.xlabel('Frequência')\n", "plt.ylabel('Palavras')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 654}, "id": "qdWpj1LZxwOy", "outputId": "8dfac7c8-875b-4694-f342-7e7fb6301ec7"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Identificando p<PERSON>-chave associadas a fake news...\n", "<PERSON><PERSON><PERSON> mais associadas a fake news:\n", "[('social', 443), ('bols<PERSON><PERSON>', 417), ('brasil', 359), ('presidente', 356), ('v<PERSON><PERSON>o', 356), ('rede', 349), ('mensagem', 323), ('circular', 321), ('pessoa', 312), ('covid', 285)]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# encontra o modelo com melhor desempenho com base na acurácia.\n", "best_model_info = max(results, key=lambda x: x[\"Acurácia\"])\n", "best_model_name = best_model_info[\"Modelo\"]\n", "best_model = trained_models[best_model_name]\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(f\"**<PERSON><PERSON>:** {best_model_name}\")\n", "print(f\"Acurácia: {best_model_info['Acurácia']:.4f}\")\n", "print(f\"Precisão: {best_model_info['Precisão']:.4f}\")\n", "print(f\"Recall: {best_model_info['Recall']:.4f}\")\n", "print(f\"F1-Score: {best_model_info['F1-Score']:.4f}\")\n", "print(f\"AUC-ROC: {best_model_info['AUC-ROC']:.4f}\")\n", "print(\"=\" * 50 + \"\\n\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CweNr0j8rIBH", "outputId": "d17d9a71-a1c0-4de9-c4a2-4e735e4c19d5"}, "execution_count": 21, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "==================================================\n", "**<PERSON><PERSON>:** Voting Classifier\n", "Acurácia: 0.9286\n", "Precisão: 0.9270\n", "Recall: 0.9332\n", "F1-Score: 0.9301\n", "AUC-ROC: 0.9805\n", "==================================================\n", "\n"]}]}, {"cell_type": "code", "source": ["# salvar o melhor modelo em disco.\n", "best_model_filename = \"best_fake_news_model.joblib\"\n", "joblib.dump(best_model, best_model_filename)\n", "print(f\"\\nMelhor modelo salvo como '{best_model_filename}'.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xzh5GHoarH9A", "outputId": "41686bca-d883-460e-9fb3-9fdaaf17bfd2"}, "execution_count": 22, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "<PERSON><PERSON> modelo salvo como 'best_fake_news_model.joblib'.\n"]}]}, {"cell_type": "code", "source": ["# carrega o modelo salvo e fazer previsões.\n", "def predict_fake_news(model, text, tfidf_vectorizer):\n", "    \"\"\"\n", "    Usa o modelo carregado para prever se um texto é fake news.\n", "    \"\"\"\n", "    processed_text = preprocess_text(text)\n", "\n", "    # yransforma o texto pré-processado em vetor TF-IDF.\n", "    text_vectorized = tfidf_vectorizer.transform([processed_text])\n", "\n", "    # faz a previsão.\n", "    prediction = model.predict(text_vectorized)\n", "    prediction_proba = model.predict_proba(text_vectorized)\n", "\n", "    predicted_class_index = int(prediction[0])\n", "\n", "    result = \"Fake News\" if predicted_class_index == 0 else \"Notícia Verdadeira\"\n", "    confidence = prediction_proba[0][predicted_class_index]\n", "\n", "    return result, confidence"], "metadata": {"id": "K9dfcr8brH7T"}, "execution_count": 23, "outputs": []}, {"cell_type": "code", "source": ["# carrega o modelo salvo e faz previsões.\n", "def predict_fake_news(model, text, tfidf_vectorizer):\n", "    \"\"\"\n", "    Usa o modelo carregado para prever se um texto é fake news.\n", "    Só será considerado Fake News se o nível de confiança for maior que 70%.\n", "    \"\"\"\n", "    processed_text = preprocess_text(text)\n", "\n", "    # transforma o texto pré-processado em vetor TF-IDF.\n", "    text_vectorized = tfidf_vectorizer.transform([processed_text])\n", "\n", "    # faz a previsão.\n", "    prediction = model.predict(text_vectorized)\n", "    prediction_proba = model.predict_proba(text_vectorized)\n", "\n", "    predicted_class_index = int(prediction[0])\n", "\n", "    confidence = prediction_proba[0][predicted_class_index]\n", "\n", "    # define o resultado com base na confiança.\n", "    if confidence > 0.7:  # confiança ma<PERSON> que 70%.\n", "        result = \"Fake News\" if predicted_class_index == 0 else \"Notícia Verdadeira\"\n", "    else:\n", "        result = \"Inconclusivo\"  # resultado quando a confiança é baixa.\n", "\n", "    return result, confidence"], "metadata": {"id": "CpbPdzJ2vo97"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["# carrega o modelo salvo.\n", "loaded_model = joblib.load(best_model_filename)\n", "print(f\"\\nModelo carregado de '{best_model_filename}'.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9IoqIRDCsLhP", "outputId": "79747dd2-0c0a-4f9b-d038-a9e6f2a1aef2"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "<PERSON><PERSON> de 'best_fake_news_model.joblib'.\n"]}]}, {"cell_type": "code", "source": ["# exemplo de uso do modelo para prever fake news.\n", "ex_text = \"Viva à Bolsonaro, viva à cloroquina! Bolsonaro faz um video provando que com seu esforço mental descobriu a cura para a covid!\"\n", "result, confidence = predict_fake_news(loaded_model, ex_text, tfidf_vectorizer)\n", "\n", "print(\"\\nResultado da previsão:\")\n", "print(f\"\\nFake ou Fato: {ex_text}\")\n", "print(f\"\\nClassificação: {result}\")\n", "print(f\"Confiança: {confidence:.4f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vMbNLzRGsLfO", "outputId": "05c8b9cb-a0c1-4909-a041-3bdcfce6cb4b"}, "execution_count": 28, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Resultado da previsão:\n", "\n", "<PERSON>ake ou Fato: Viva à Bolsonaro, viva à cloroquina! Bolsonaro faz um video provando que com seu esforço mental descobriu a cura para a covid!\n", "\n", "Classificação: Fake News\n", "Confiança: 0.8609\n"]}]}, {"cell_type": "code", "source": ["# exemplo de uso do modelo para prever fake news.\n", "ex_text = \"A região está registrando o maior aumento de casos de covid no mundo, e o período de festas pode aumentar os números, alerta a OMS, que pede vacinação acelerada e medidas contínuas de saúde pública.\"\n", "result, confidence = predict_fake_news(loaded_model, ex_text, tfidf_vectorizer)\n", "\n", "print(\"\\nResultado da previsão:\")\n", "print(f\"\\nFake ou Fato: {ex_text}\")\n", "print(f\"\\nClassificação: {result}\")\n", "print(f\"Confiança: {confidence:.4f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2353J1PVtmxR", "outputId": "c2e63d0b-a1eb-405b-de4a-8de1ff486356"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Resultado da previsão:\n", "\n", "Fake ou Fato: A região está registrando o maior aumento de casos de covid no mundo, e o período de festas pode aumentar os números, alerta a OMS, que pede vacinação acelerada e medidas contínuas de saúde pública.\n", "\n", "Classificação: Inconclusivo\n", "Confiança: 0.6132\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "0I-n-LbCsLZ6"}, "execution_count": 27, "outputs": []}]}