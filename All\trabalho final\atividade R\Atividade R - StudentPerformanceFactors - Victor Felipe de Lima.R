
# Nome: <PERSON>
# Tema/Dataset: Student Performance Factors

#file.choose()
df_csv = read.csv(file.choose())
df_csv

#nome das colunas
colnames(df_csv)

#dimensão da tabela
dim(df_csv)

#medidas de tendência central
#média
mean(df_csv $ Previous_Scores)
mean(df_csv $ Exam_Score)

#mediana
median(df_csv $ Previous_Scores)
median(df_csv $ Exam_Score)

#moda
desem_na<-df_csv $ Previous_Scores
desem_na

desem_nf<-df_csv $ Exam_Score
desem_nf

f.desem_na<-table(desem_na)
f.desem_nf<-table(desem_nf)

fmax_na<-max(f.desem_na)
fmax_na

fmax_nf<-max(f.desem_nf)
fmax_nf

#encontrar o valor correspondente da frequência máxima
moda1<-(names(f.desem_na[f.desem_na == fmax_na]))
moda1

moda2<-(names(f.desem_nf[f.desem_nf == fmax_nf]))
moda2

#medidas de variabilidade
#variância
var(df_csv $ Previous_Scores)
var(df_csv $ Exam_Score)

#desvio-padrão
dp1<-sd(df_csv $ Previous_Scores)
dp1

dp2<-sd(df_csv $ Exam_Score)
dp2

#coeficiente de variação
coef_var1<-dp1/mean(df_csv $ Previous_Scores)*100
coef_var1

coef_var2<-dp2/mean(df_csv $ Exam_Score)*100
coef_var2

#medidas de posição
#sumarização de variáveis
summary(df_csv $ Previous_Scores)
summary(df_csv $ Exam_Score)

#percentil Exames Anteriores
percentil1_1<-quantile((df_csv $ Previous_Scores), probs = 0.01)
percentil1_1

percentil1_25<-quantile((df_csv $ Previous_Scores), probs = 0.25)
percentil1_25

percentil1_50<-quantile((df_csv $ Previous_Scores), probs = 0.50)
percentil1_50

percentil1_75<-quantile((df_csv $ Previous_Scores), probs = 0.75)
percentil1_75

percentil1_100<-quantile((df_csv $ Previous_Scores), probs = 1)
percentil1_100

#percentil Exame Final
percentil2_1<-quantile((df_csv $ Exam_Score), probs = 0.01)
percentil2_1

percentil2_25<-quantile((df_csv $ Exam_Score), probs = 0.25)
percentil2_25

percentil2_50<-quantile((df_csv $ Exam_Score), probs = 0.50)
percentil2_50

percentil2_75<-quantile((df_csv $ Exam_Score), probs = 0.75)
percentil2_75

percentil2_100<-quantile((df_csv $ Exam_Score), probs = 1)
percentil2_100


###############################################################################################################################


# Frequência da Variável Exames Anteriores
# Frequência Absoluta
fa1<-table(df_csv $ Previous_Scores)
fa1

fa2<-table(df_csv $ Exam_Score)
fa2

# Frequência Relativa
fr1<-100*prop.table(fa1)
fr1

fr2<-100*prop.table(fa2)
fr2

# Arredondamento Frequência
fr1<-round(fr1, digits = 2)
fr1

fr2<-round(fr2, digits = 2)
fr2

# Adicionando Linhas de Total para Frequências (Frequência Acumulada)
fa1<-c(fa, sum(fa1))
fr1<-c(fr, sum(fr1))

fa2<-c(fa, sum(fa2))
fr2<-c(fr, sum(fr2))

tabela1<-cbind(fa1, fr1)
tabela1

tabela2<-cbind(fa2, fr2)
tabela2

# Tabela Contingência
cont_fa1<-table(df_csv $ Previous_Scores, df_csv $ Exam_Score)
cont_fa1

cont_fa2<-table(df_csv $ Exam_Score, df_csv $ Previous_Scores)
cont_fa2

cont_fr1<-100*prop.table(cont_fa1)
cont_fr1

cont_fr2<-100*prop.table(cont_fa2)
cont_fr2

# Gráfico de Barras
# GB Simples
barplot(fr1, beside = T)
barplot(fr2, beside = T)

# GB Contingência
barplot(cont_fr1, beside = T)
barplot(cont_fr2, beside = T)

# Gráfico de Setores/Torta
# Frequência Relativa
fa1_2<-table(df_csv $ Previous_Scores)
fr1_2<-100*prop.table(fa1_2)
pie(fr1_2)

fa2_2<-table(df_csv $ Exam_Score)
fr2_2<-100*prop.table(fa2_2)
pie(fr2_2)

# Gráfico de Dispersão
exames = read.csv(file.choose())
#View(exames)

plot(exames $ Previous_Scores,
     main = "Scatter Plot",
     xlab = "Index", 
     ylab = "Previous Scores", 
     col = "blue", 
     pch = 19)

# Gráfico BOXPLOT
boxplot(exames $ Previous_Scores)
boxplot(df_csv $ Exam_Score)

# Gráfico BOXPLOT com mais de uma variável
boxplot(exames $ Previous_Scores ~ exames $ Exam_Score)
boxplot(exames $ Exam_Score ~ exames $ Previous_Scores)

# Teste de Normalidade
# Histograma
hist(exames $ Previous_Scores, main = "Histograma de Exames Anteriores", xlab = "Número de Exames", ylab = "Frequência")
hist(exames $ Exam_Score, main = "Histograma de Exames Finais", xlab = "Número de Exames", ylab = "Frequência")

# Teste Shapiro-Wilk
options(scipen = 9999)
shapiro.test(exames $ Previous_Scores)
shapiro.test(exames $ Exam_Score)

# Correlação de Pearson (paramétrica)
# gerar números aleatórios
set.seed(123)

# número de observações
n<-100

# gerando dados normalmente distribuidos para X
a<-rnorm(n, mean = 50, sd = 10)

# gerando dados normalmente distribuidos para Y
b<-2*a+rnorm(n, mean = 0, sd = 5)

# visualizando os dados
plot(a, b, main = "dispersão dos dados", xlab = "x", ylab = "y")

cor_pearson<-cor(a, b, method = "pearson")
cor_pearson

cor.test(a, b, method = "pearson")

# Correlação de Spearman (não-paramétrica)
plot(exames $ Previous_Scores, exames $ Exam_Score, main = "Dispersão dos Dados", xlab = "Visualizações", ylab = "[eixo]")
cor_spearman1<-cor(exames $ Previous_Scores, exames $ Exam_Score, method = "spearman")
cor_spearman1

plot(exames $ Exam_Score, exames $ Previous_Scores, main = "Dispersão dos Dados", xlab = "Visualizações", ylab = "[eixo]")
cor_spearman2<-cor(exames $ Exam_Score, exames $ Previous_Scores, method = "spearman")
cor_spearman2

# Regressão Linear
# inspecionar a linearidade
plot(exames $ Previous_Scores, exames $ Exam_Score)

m1<-lm(exames $ Previous_Scores ~ exames $ Exam_Score)
hist(residuals(m1))

plot(exames $ Exam_Score, exames $ Previous_Scores)

m2<-lm(exames $ Exam_Score ~ exames $ Previous_Scores)
hist(residuals(m2))

# Teste de Normalidade
shapiro.test(residuals(m1))
shapiro.test(residuals(m2))

# Regressão Linear (Dados Hipotéticos)
set.seed(123)
x <- exames $ Previous_Scores
y <- exames $ Exam_Score

# ajuste do modelo linear
m1_2<-lm(y~x)

plot(x, y, main = "Linear Regression - Previous Scores vs Exam Score",
     xlab = "Previous Scores", ylab = "Exam Score", pch = 19, col = "blue")

# adicionando a reta de mínimos quadrados
abline(m1_2, col = "red", lwd = 2)

# equação da reta
coeficientes<-coef(m1_2)

cat ("Equação da Reta: Y = ", round(coeficientes[1], 2), " + ", round(coeficientes[2], 2), " * X\n")

summary(m1_2)

# Regressão Logística
set.seed(123)

df <- read.csv("StudentPerformanceFactors.csv")

# Transformar a variável Exam_Score em binária (por exemplo, classificar >= 70 como sucesso)
df$Success <- ifelse(df$Exam_Score >= 70, 1, 0)

# Ajustar o modelo de regressão logística
modelo_log <- glm(Success ~ Previous_Scores, data = df, family = binomial)

# Resumo do modelo
summary(modelo_log)

# Plotando a relação
plot(df$Previous_Scores, df$Success, main = "Logistic Regression - Previous Scores vs Success",
     xlab = "Previous Scores", ylab = "Success (0 or 1)", pch = 19, col = "blue")

# Adicionando a curva de regressão logística
curve(predict(modelo_log, data.frame(Previous_Scores = x), type = "response"), 
      add = TRUE, col = "red", lwd = 2)

