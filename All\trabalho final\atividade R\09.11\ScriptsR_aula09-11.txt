#lendo arquivos CSV 

#Função file.choose()

file.choose()

#lendo arquivos csv

df_csv = read.csv(file.choose())
df_csv

#cabeçalho das colunas
colnames(df_csv)

#dimensão da planilha
dim(df_csv)

#MEDIDAS DE TENDÊNCIA CENTRAL

#mediana salarial em USD
median(df_csv$salary_in_usd)

#Média salarial em USD
mean(df_csv$salary_in_usd)

#moda salarial em USD
#não há função pronta para a moda, então para encontrá-la(s) começamos fazendo uma tabela de frequência

sal_usd<-(df_csv$salary_in_usd)
sal_usd

fre.sal_usd<-table(sal_usd)
fre.sal_usd

#max para ver o máximo de repetição (frequência máxima)
fmax<-max(fre.sal_usd)
fmax

#encontrar o valor correspondente a frequencia máxima
moda<-(names(fre.sal_usd[fre.sal_usd == fmax]))
moda

#MEDIDAS DE VARIABILIDADE

#Variância
var(df_csv$salary_in_usd)

#desvio-padrão

dp<-sd(df_csv$salary_in_usd)

#coeficiente de variação sd(dados)/mean(dados)*100

coef_variação<-dp/mean(df_csv$salary_in_usd)*100
coef_variação

#MEDIDAS DE POSIÇÃO

#Percentil

percentil_1<- quantile((df_csv$salary_in_usd), probs = 0.01)
percentil_1

#SUMMARY: Mínimo, 1ºQuartil, Média, Mediana, 3º Quartil, Máximo
summary(df_csv$salary_in_usd)