# -*- coding: utf-8 -*-
"""Untitled0.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1nJPnm1l54TXLHU2G-FP2kr3CCeG8TeQP
"""

import pandas as pd

# Load the dataset
diabetic_data = pd.read_csv('/content/diabetic_data.csv')

# Load the mapping data
ids_mapping = pd.read_csv('/content/IDs_mapping.csv')

# Display the first few rows of the dataset
diabetic_data.head()

# Generate a summary of the dataset
data_summary = diabetic_data.describe(include='all').transpose()

# Calculate the number of missing values for each column
data_summary['missing_values'] = diabetic_data.shape[0] - data_summary['count']

# Display the data summary
data_summary[['count', 'unique', 'top', 'freq', 'mean', 'std', 'min', '25%', '50%', '75%', 'max', 'missing_values']]

ids_mapping.head(20)

# Calculate the number and percentage of missing values for each column
missing_values = diabetic_data.replace("?", pd.NA).isna().sum()
missing_percentage = (missing_values / diabetic_data.shape[0]) * 100

# Create a dataframe to display the results
missing_data = pd.DataFrame({"Missing Values": missing_values, "Percentage": missing_percentage})
missing_data = missing_data[missing_data["Missing Values"] > 0].sort_values(by="Percentage", ascending=False)

missing_data

# Drop the 'weight' column
diabetic_data.drop(columns=['weight'], inplace=True)

# Impute columns with 'Unknown' for missing values
cols_to_impute_unknown = ['medical_specialty', 'payer_code']
diabetic_data[cols_to_impute_unknown] = diabetic_data[cols_to_impute_unknown].replace('?', 'Unknown')

# Impute columns with mode for missing values
cols_to_impute_mode = ['race', 'diag_1', 'diag_2', 'diag_3']
for col in cols_to_impute_mode:
    mode_value = diabetic_data[col].mode()[0]
    diabetic_data[col] = diabetic_data[col].replace('?', mode_value)

# Verify if all missing values have been addressed
missing_values_post = diabetic_data.replace("Unknown", pd.NA).isna().sum()
missing_values_post[missing_values_post > 0]

# Transform the 'readmitted' column to binary outcome
diabetic_data['readmitted'] = diabetic_data['readmitted'].apply(lambda x: 1 if x == '<30' else 0)

# Check the distribution of the transformed 'readmitted' column
diabetic_data['readmitted'].value_counts()

# Define a function to group less frequent categories
def group_less_frequent_categories(data, column, threshold=0.01):
    # Calculate the frequency of each category
    category_freq = data[column].value_counts(normalize=True)

    # Find categories with frequency less than the threshold
    less_frequent_categories = category_freq[category_freq < threshold].index

    # Group these categories under 'Other'
    data[column] = data[column].apply(lambda x: 'Other' if x in less_frequent_categories else x)

# Apply the function to the selected columns
cols_to_group = ['diag_1', 'diag_2', 'diag_3', 'medical_specialty']
for col in cols_to_group:
    group_less_frequent_categories(diabetic_data, col)

# Check the number of unique values after grouping
diabetic_data[cols_to_group].nunique()

# Identify categorical columns for one-hot encoding
categorical_cols = diabetic_data.select_dtypes(include=['object']).columns.tolist()

# One-hot encode the categorical columns
diabetic_data_encoded = pd.get_dummies(diabetic_data, columns=categorical_cols, drop_first=True)

# Display the shape of the dataset after encoding
diabetic_data_encoded.shape

from sklearn.model_selection import train_test_split

# Define the features (X) and the target variable (y)
X = diabetic_data_encoded.drop('readmitted', axis=1)
y = diabetic_data_encoded['readmitted']

# Split the data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Display the shape of the training and testing sets
(X_train.shape, X_test.shape, y_train.shape, y_test.shape)

from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

# Initialize and train the Logistic Regression model
logistic_model = LogisticRegression(max_iter=1000, random_state=42)
logistic_model.fit(X_train, y_train)

# Predict on the training set
logistic_train_predictions = logistic_model.predict(X_train)

# Calculate and display the training accuracy
logistic_train_accuracy = accuracy_score(y_train, logistic_train_predictions)
logistic_train_accuracy

from sklearn.ensemble import RandomForestClassifier

# Initialize and train the Random Forest model
random_forest_model = RandomForestClassifier(random_state=42)
random_forest_model.fit(X_train, y_train)

# Predict on the training set
rf_train_predictions = random_forest_model.predict(X_train)

# Calculate and display the training accuracy
rf_train_accuracy = accuracy_score(y_train, rf_train_predictions)
rf_train_accuracy

from sklearn.ensemble import GradientBoostingClassifier

# Initialize and train the Gradient Boosting model
gb_model = GradientBoostingClassifier(random_state=42)
gb_model.fit(X_train, y_train)

# Predict on the training set
gb_train_predictions = gb_model.predict(X_train)

# Calculate and display the training accuracy
gb_train_accuracy = accuracy_score(y_train, gb_train_predictions)
gb_train_accuracy

from sklearn.metrics import precision_score, recall_score, f1_score

# Define a function to evaluate the models on the test set
def evaluate_model(model, X_test, y_test):
    # Predict on the test set
    predictions = model.predict(X_test)

    # Calculate metrics
    accuracy = accuracy_score(y_test, predictions)
    precision = precision_score(y_test, predictions)
    recall = recall_score(y_test, predictions)
    f1 = f1_score(y_test, predictions)

    return accuracy, precision, recall, f1

# Evaluate each model
logistic_metrics = evaluate_model(logistic_model, X_test, y_test)
rf_metrics = evaluate_model(random_forest_model, X_test, y_test)
gb_metrics = evaluate_model(gb_model, X_test, y_test)

# Store the results in a DataFrame for display
evaluation_results = pd.DataFrame({
    'Metric': ['Accuracy', 'Precision', 'Recall', 'F1 Score'],
    'Logistic Regression': logistic_metrics,
    'Random Forest': rf_metrics,
    'Gradient Boosting': gb_metrics
})

evaluation_results

# Display the evaluation results table for reference
evaluation_results

import matplotlib.pyplot as plt

# Set up the figure and axes
fig, ax = plt.subplots(figsize=(10, 6))

# Data for plotting
models = evaluation_results.columns[1:].tolist()
accuracies = evaluation_results.loc[evaluation_results['Metric'] == 'Accuracy', models].values[0]

# Bar chart for model accuracy comparison
ax.bar(models, accuracies, color=['blue', 'green', 'red'])
ax.set_title('Model Accuracy Comparison', fontsize=16)
ax.set_xlabel('Models', fontsize=14)
ax.set_ylabel('Accuracy', fontsize=14)
ax.set_ylim(0.88, 0.89)
ax.set_yticklabels(['{:.2f}%'.format(x*100) for x in ax.get_yticks()])  # Display y-axis ticks in percentage format

# Display the actual accuracy values on top of the bars
for i, v in enumerate(accuracies):
    ax.text(i, v + 0.001, '{:.2f}%'.format(v*100), ha='center', va='bottom', fontsize=12)

plt.tight_layout()
plt.show()

# Set up the figure and axes
fig, ax = plt.subplots(figsize=(12, 7))

# Data for plotting
metrics = ['Precision', 'Recall', 'F1 Score']
bar_width = 0.25
index = range(len(metrics))

# Grouped bar chart for precision, recall, and F1 score comparison
for i, model in enumerate(models):
    values = evaluation_results.loc[evaluation_results['Metric'].isin(metrics), model].values
    ax.bar([p + bar_width*i for p in index], values, width=bar_width, label=model)

ax.set_title('Precision, Recall, and F1 Score Comparison', fontsize=16)
ax.set_xlabel('Metrics', fontsize=14)
ax.set_ylabel('Score', fontsize=14)
ax.set_xticks([p + bar_width for p in index])
ax.set_xticklabels(metrics)
ax.legend()

# Display the actual score values on top of the bars
for i, model in enumerate(models):
    values = evaluation_results.loc[evaluation_results['Metric'].isin(metrics), model].values
    for j, v in enumerate(values):
        ax.text(j + bar_width*i, v + 0.01, '{:.2f}'.format(v), ha='center', va='bottom', fontsize=10)

plt.tight_layout()
plt.show()

# Select a few random samples from the test set
sample_test_cases = X_test.sample(n=5, random_state=42)
sample_actual_outcomes = y_test[sample_test_cases.index]
sample_predictions = gb_model.predict(sample_test_cases)

# Display the actual vs. predicted outcomes for the samples
sample_comparison = pd.DataFrame({
    'Actual Outcome': sample_actual_outcomes,
    'Predicted Outcome': sample_predictions
})
sample_comparison['Actual Outcome'] = sample_comparison['Actual Outcome'].map({0: 'No Readmission', 1: 'Readmission'})
sample_comparison['Predicted Outcome'] = sample_comparison['Predicted Outcome'].map({0: 'No Readmission', 1: 'Readmission'})

sample_comparison

