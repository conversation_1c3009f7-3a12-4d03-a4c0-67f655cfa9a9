então é assim ó, de um modo geral né
a ideia é essa mesmo né, e mais importante

assim é como conduzir o texto tá, então pensa o seguinte
ó é quando cê fala assim, ah de investigar
desenvolver tal
quando cê tá no mestrado cê tem um tiro curto
por que que cê vai passar esse ano
praticamente o ano inteiro fazendo disciplinas
você vai tá envolvido com as disciplinas
trabalho pra entregar
então acho que se for fazer a conta
tem que fazer tipo
três disciplinas num semestre e duas no outro

então acaba sendo meio puxado assim sabe
então cê vai ter pouco tempo pra conseguir avançar com o projeto como a gente gostaria né

então quando a gente fala assim: ah eu vou fazer uma uma
uma revisão e depois vou fazer uma aplicação tal

é muita coisa pro mestrado, muita coisa mesmo

então eu já falo isso antes porque senão depois você vai se frustrar

fala assim: cara, eu achava que dava para fazer um monte de coisa e não dá

uma outra coisa é assim ó: sempre que você for fazer alguma coisa, principalmente na ciência da informação, a palavra desenvolver remete muito, cara, a coisa relacionada a programação a esse tipo de coisa, ter um produto ou tem um serviço, alguma coisa assim

mesmo que você chegue nisto, não se comprometa com isso. entendeu?

não precisa disso na ciência da informação, a gente precisagente precisa avaliar coisa, identificar coisa; mas a gente não precisa entregar, produz entregar serviço

se isso no final acontecer, cara, todo mundo vai achar maravilhoso; agora se você se compromete a fazer isso e no final você não consegue fazer, aí cê vai tá em débito com o negócio, e a gente não precisa propor isso né

então lá no comecinho né, fala: investigar e desenvolver sistemas avançados de processamento, tal não sei o quê

então eu acho que a ideia é uma abordagem diferente né; então assim: identificar se os sistemas, identificar se aplicações de llm são capazes de... então assim é muito mais identificar...

ah eu coloquei no comentário do texto desse jeito: avaliar o comportamento essas IAs

então assim, avaliar o comportamento é fazer o quê, cara, cê tem várias maneiras de avaliar; cê pode avaliar o ponto de vista tecnológica, aplicação; cê pode avaliar do ponto de vista de
uma revisão... tudo isso é avaliar né

então assim, eu acho que tem que meio por essa linha; que é identificar né, analisar e identificar se realmente o uso de llm né com rag, com agentes llms; são capazes, né, de responder a resposta a respeito de um escopo fechado de conjunto de dados da área da saúde

então fazendo isso dá para gente depois especificar várias coisas

a partir disso, então assim... cê colocou lá objetivo específico conduziu um estudo métrico...

que que cê acha que é um estudo métrico (?)
porque estudo métrico existe na área da ciência da informação, a gente tem doze GTS de estudo e um deles é estudo métrico; então assim, é uma área específica da ciência da informação

(guess2_deleted)

porque assim, quando a gente fala estudo método na ciência da informação principalmente, é um estudo bibliométrico; vamos dizer, de indicadores, né; então fazer um estudo de indicadores

então acho que no seu caso né... quando você fala: realizar uma revisão sistemática né, não é um estudo métrico

então assim, eu tiraria essa parte de estudo método; então é o que (?): fazer uma revisão de literatura mesmo sobre o tema né, é em anotações clínicas para entender o estado da arte

cara, eu vou te falar uma coisa: isso já é um mestrado, só pra você saber, tá

então isso já seria suficiente pra nossa área para que isso fosse um mestrado... então beleza

você já fez revisão sistemática alguma vez ou não (?)

(guess2_deleted)

porque assim ó, porque a revisão sistemática, ela tem uma metodologia bem definidinha assim né; que é definir o protocolo, definir como que é o protocolo, quais são os termos, quais são as suas características de inclusão, de exclusão, tal não sei o que não sei o que

então assim, beleza se você sabe fazer, tá ótimo; a gente precisa definir qual é esse protocolo, definir junto né, que é justamente isso, ver como é que isso tá atuando realmente na área de ciência da informação; como que está atuando na área de saúde, pra verificar né, é, o que que é o escopo disso né, quais são os problemas, quais são as as soluções, que que a gente tá vendo, quais são os algoritmos tá

então é pra identificar o estado da arte a partir de uma revisão sistemática

se você quiser, eu posso pedir pra uma aluna minha de doutorado
que é a Amanda, te ajudar ou marcar um dia pra te explicar uma revisão sistemática; se você sabe tudo certo, não; ela é super especialista

então tem até um sistema que se usa, não sei se já viu o tema que usa em São Carlos (?) não que é o --

tem até um sisteminha pra fazer isso, que ajuda pra caramba pra fazer
então assim, a gente pode marcar isso

então assim, no seu texto quando você fala disso: o objetivo específico, o que que é objetivo (?): o seu objetivo é identificar o estado, ou analisar o estado, verificar o estado da arte, aplicação de llm em estudos clínicos, a partir de uma revisão sistemática

a revisão sistemática não é o objetivo, é o método que você faz
aí então assim, qual que é o seu objetivo (?): eu quero verificar o estado da arte desse negócio aqui

aí depois você fala: projetar e implementar sistemas baseados em llm, para processar anotações clínicas não estruturadas, tal
aí, depois -- sistemas desenvolvidos por meio de estudo de caso com dados clinicos reais, sinteticos, verificando sua eficácia e tal
então esses dois elementos, 2 e 3, a primeira coisa que tem que ser feita é levantar, identificar, aplicar o estudo de caso, certo (?), mas pra isso a gente precisa primeiro preparar os dados, preparar o corpus... vai dar um trabalho, cara

não sei como é que vai ser isso, eu não sei se o corpos que a gente vai ter... são prontuários escritos, se tão em sistema, se vão precisar ser digitalizados

então assim, eu não sei qual que é o esquema do corpus, entendeu (?); então a primeira coisa, é realmente pegar esse, então se a gente vai segmentar isso, então, por exemplo: trabalhar isso com dado estruturado... é dado não estruturado, eu sei, mas, por exemplo, eu sei que quando eu pego um dado não estruturado, mas eu pego, por exemplo, uma namnésia, é uma namnésica (!), mesmo que seja um texto escrito, é uma namnésia

então passa a ser, entre aspas, fazer um dado estruturado
ah eu tenho lá um histórico familiar, um histórico familiar é uma informação estruturada; eu só não tenho lá, não é um banco de dados né, ah eu tenho pai, mãe... não. é um texto escrito, mas é um histórico familiar, então assim eu tenho campos...

aí precisa ver assim: como que eu lido com isso tá
então assim, eu acho que esses 2 e 3, a primeira coisa é o quê (?): é analisar o corpus de dados para ver como que eu posso trabalhar com isso, para que eu possa colocar isso dentro de um sistema baseado em llm... como que eu posso oferecer isso...

então aí já vamo criar várias coisas, cara... quais são as melhores práticas para fazer isso (?), como que realmente é
o consolido esses dados (?), quanto de dados suficiente (?), quanto que a minha llm consegue processar desses dados (?)

então, por exemplo: ah eu vou precisar toquenizar isso; ah vou tocanizar isso
tá, então quantos prontuários eu consigo toquenizar com a minha llm (?); se eu usar a llm x eu consigo tocanizar tanto, se eu conseguir usar a llm y eu consigo toquenizar tanto

será que se eu usar essa llm eu consigo ter, sei lá, dez mil prontuários (?), ele vai conseguir processar (?)

então assim esse tipo de coisa são perguntas... entendeu (?) que
que o próprio desenvolvimento vai nos levar a ter... então tem muitas perguntas a serem feitas né, dentro de cada um desses contextos, que não tão aqui, por exemplo, que são -- que a gente pode se pautar

então, por exemplo, se eu parasse aqui de novo, já tava resolvido mestrado (!)

que é o que (?): identificar o corpus, como que llm atua sobre esse corpus, se ela realmente tá conseguindo processar esse corpus... eu não tô falando nem de resultado tá (?); eu tô falando só de como montar uma estrutura de llm e ver se ela consegue, a partir disso, com os algoritmos que eu tenho disponíveis hoje né, pago e não pago, como que ele atua sobre o conjunto de dados, como que eu organizo, como que é o processo, qual é o custo disso, custo de processamento, curso de máquina, curso de armazenamento...

então a gente tem várias coisas pra pensar, nesse contexto aí que é justamente, primeiro, identificar como que eu monto esse corpus sem saber que a gente não sabe... eu mandei uma mensagem pra professora Silvana pra ver se ela consegue arrumar uma conversa pra gente com alguém lá da unespe da parte de -- lá que é a parte de saúde... porque se não der certo eu vou ter que procurar alguém aqui em Ribeirão, tentar uma coisa assim ou tentar algum outro contato né, ou na famema aí em Marília, enfim, eu vou tentar achar uma solução pra gente conseguir viabilizar algum tipo de tratamento, algum tipo de dado desse tipo

então assim esse é um contexto, então vamo pro próximo passo onde cê coloca assim no um objetivo específico: desenvolver mecanismo transparência e de explicabilidade para respostas geradas pelo sistema, isso aqui já é um outro caso, cara (!)

imagina que tem em muita coisa pra ser feita, porque a hora que cê vai interpretar, por exemplo né (?): a pessoa coloca uma pergunta, cê vai precisar montar uma estrutura de prompt né, que é um cadeamento lá dentro da llm, pra poder botar os nossos promts pra juntar com o texto da pessoa pra que eu conduza uma pergunta mais adequada pra llm... então aqui já é um outro estudo, percebe (?)

só essa parte de estudar né, como que eu lido com isso, porque, por exemplo, a pessoa faz uma pergunta vou ter que melhorar isso
como que eu interajo, como que eu não interajo, como que eu pergunto, como que eu respondo, tal

então assim: como que essa como que isso pode ser feito

aí depois vai pro 5: examinar questões éticas e privacidade
relacionada a uso -- cara, isso aqui eu ia falar pra você retirar (!), porque isso aqui é muita coisa... essa questão ética, questão de privacidade; isso... se der tempo... mais pra frente, isso pode virar um estudo pra virar um artigo, por exemplo... ou virar um estudo pro doutorado, por exemplo

e no final: elaborar um guia de boas práticas pra integração de sistemas baseados ambientes clínicos e tal; isso aqui, beleza é um produto, cara, é ótimo ter isso aqui, mas eu não sei se a gente consegue chegar neste nível, entendeu (?)

eu queria, por exemplo, pensar em alguma coisa assim: ah eu quero testar, uma llm responde bem -- um conjunto de dados que eu tenho... que tipo de algoritmo eu vou usar pra --

como que eu posso comparar, estudo comparativo, por exemplo, mas acho que não vou chegar nesse nível

eu acho que as primeiras coisas, que os primeiros passos, a gente pode até chegar em resultados, mas acho que os primeiros passos é principalmente analisar corpus e ver como que a llm atua sobre esses corpos...

então escolher algoritimos, escolher os hiperparametros, que eu tenho dentro desses algoritimos; ver como que eu reajo isso dentro do conjunto de dados... porque se a gente fizer isso
eu já tenho meio caminho andado pra poder fazer a outra parte
entendeu (?) isso depois a gente -- pra um doutorado

então eu acho que esta proposta parte, primeiro, de uma revisão sistemática bem feitinha e de pegar o corpus e verificar a estruturação de uma llm, corpus, ver como a llm reaje sobre um corpus, podia parar aí 

no final, elaborar um guia de boas praticas para definir corpus de área da saúde pra receber atuação de llm, e aí finaliza

assim, deu tempo, a gente conseguiu terminar, deu tudo certo
tá beleza, cara, vamo avançar pra mais coisa (?), vamo (!)

mas na proposta, a proposta que eu tô te falando já é uma proposta suficiente para ser aceita no projeto fapespe, que seja aceita como um mestrado, como um bom mestrado

a gente não precisa lançar mais do que isso você tem um ano para fazer... não acaba sendo um ano, você vai fazer praticamente
vai fazer a revisão sistemática junto com as disciplinas... vai começar lotar de trabalho pra fazer... demanda tempo... porque todos vão te pedir um artigo, uma coisa assim, então isso vai demandar tempo para você trabalhar

então esse é um contexto, por isso que eu queria conversar com você né, esse é um contexto de conseguir ter algo que é palpável, algo diz o que é claro pra gente o que a gente vai fazer, que você consegue dar conta de fazer e se terminar, um ano cê terminou de fazer tudo beleza, então não tem problema, vamo continuar avançando contra as coisa, mas a gente não se compromete com fazer

é diferente, por exemplo, eu te falar: ah faz uma proposta vai chegar na fapespe e falar assim: ah não isso aqui é muito simples de fazer; não (!), o que eu tô te falando são um conjunto de coisas suficiente pra fapespe entender que é um
estudo necessário

então assim, principalmente quando a gente vai trabalhar aí com aplicação, com estudo de caso, então dá pra pensar em coisas desse tipo; então eu acho que podia ir um pouco nesta linha de atuação

(guess2_deleted)

exatamente (!), então essa é uma coisa que é assim, eu não quero te frustrar, ah eu queria fazer um monte de coisa e o professor tá falando pra fazer menos, não; cara, dá para gente avançar, mas é que assim ó, eu prefiro ter um negócio plausível e negócio possível -- né

e quando a gente vai falar de estudar, por exemplo, o corpus... cara, isso já é um estudo que já vai dar muito pano para manga, pra estudar isso... porque tem que pegar esse corpus, cara,
e minimamente tentar fazer esse corpus ver como que a llm vai reagir a esse corpo

porque se a gente tá achando que o negócio vai ser fácil, e não vai (!), entendeu (?)

a hora que cê vai trabalhar com isso, cara, como que tá esses dados (?), os dados tão balanceados (?): não tão, tem que melhorar, tem que trazer mais dados, tem que trazer casos resolvidos, casos não resolvidos... tem muita coisa pra pensar no conjunto de dados e experimentando como que a llm responde

se a gente for ficar se preocupando com o resultado lá na frente né, eu vou passar por essa parte e essa parte é importante... então eu prefiro aprofundar um pouco mais no estudo do corpus, como a llm tá conseguindo realmente atuar sobre esse conjunto de dados e ter respostas, do que avançar e deixar essa parte pra trás

então assim, fazer um conjunto de melhores práticas, de determinação de como que eu seleciono o conjunto de dados da área da saúde pra poder trazer... isso já é uma contribuição muito grande

eu acho que podia ir nesta linha, que que cê acha (?)

(guess2_deleted)

então assim, a gente podia ir nessa linha, e aí assim, a gente podia propor esse estudo do corpus como um estudo de caso mesmo,
para isso nós precisamos ter um ente parceiro... não dá para fazer o estudo de caso sem ter um ente parceiro, a não ser que a gente tenha uma base de saúde pública... que eu não sei

(guess2_deleted)

é. então acho difícil ter mesmo... então assim, a gente pra gente conseguir ter um estudo de caso, a gente precisa ter um parceiro, pra mandar o projeto pra fapespe, então assim se você quiser pegar o modelo lá da fapespe, e começar a rascunhar... baseado nisso que você fez, só que lá um pouquinho diferente, porque na verdade a estrutura diferente né

então a sugestão: refaz isso aqui, nisso que a gente conversou... aí a gente conversa de novo... a partir disso você começa a montar um projeto fapespe; monta um projeto fapespe como se a gente tivesse um ente, bota lá hospital x,tá (?), aí nós vamos atrás desse enter pra ver se a gente consegue um parceiro pra conseguir fazer isso, se a gente conseguir, ótimo, a gente faz como um estudo de caso, porque um estudo de caso é uma metodologia né

então a gente vai fazer, proceder um estudo de caso com dados reais aí

então assim, acho que a ideia é reformular pensando dessa maneira, e assim, ó sempre, nunca é desenvolver, aplicar... é sempre assim: identificar, verificar, possibilidades, se é possível tal; assim é, avaliar o comportamento... então identificar o comportamento, por quê (?), porque aí tudo que a gente desenvolver, porque a área da ciência da informação não pede que a gente desenvolva nada... se a gente desenvolver, cara, é um ganho... se a gente não desenvolver, a gente não se comprometeu... não é igual a área de engenharia que cê precisa aplicar... não, aqui a gente não precisa né; a gente vai fazer um estudo teórico, e se a gente conseguir avançar pra aplicação, e aqui no estudo de caso vai ter uma aplicação né,

então assim, a parte já de fazer a revisão sistemática já é muito boa e depois a parte de pensar no corpus e pensar numa implementação de llm pra verificar como que essa llm atua sobre esses dados... beleza (?)

(guess2_deleted)

quando eu falo potencializar os outros dados
como que é potencializar
a gente vai passar até indicadores
ou a gente vai passar a responder perguntas
a gente consegue fazer uma
pergunta mais específica
que tem uma resposta baseado em dados
de evidência né
então assim eu
eu eu
eu prefiro uma coisa mais
uma clareza de como que isso acontece né
o potencializar
facilitar e são muito superficiais assim né
como que ele facilita informação específica
é porque
o pessoal da saúde vai conseguir interagir
com uma interface
onde ele faça perguntas e
as perguntas baseadas não sei o que
assim é muito mais esse tipo de de de
de coisa

e aqui no final, eu tirei na metodologia, você vai ver que eu coloquei lá: o estudo transita por um olhar de ciência da informação --

porque indexação e recuperação são sub áreas da ciência da informação; em prontuários médicos
ou seja
informação em saúde
eu tirei o que tá falando essa tecnologia
porque já tá
isso já tá implícito
EU tô falando de 2
subárias
da
6ª
informação
se relaciona com computação

ah e aí lá embaixo o resultado esperado: capaz de processar as anotações clínicas com alta acurácia, não (!), o objetivo é avaliar nessa aplicação de llm tal, em dados prontuário médico, avaliar isso que está acontecendo, a gente não pode prometer que a gente vai processar a análise clínica, não, a gente vai verificar se isso é possível de ser feito, como que se comporta, pode ser que seja bom ou pode ser ruim, então não tô propondo que a gente vai botar um produto que vai resolver a área médica,
não, são estudos, a pesquisa nasce assim né, verificar se isso parece, ah faz sentido vamo vamo vamo avançar vamo avançar

como a gente não tá vendendo isso pra ninguém, como eu não tô vendendo projeto no hospital pra ninguém, cobrando dinheiro
nada; a gente começa pé no chão, diagnóstico, verificar, identificar, começa a ter mais noção como é que isso funciona, a gente vai avançando até que a gente chegue nesse nível de realmente capaz de processar bem, mas a gente não vai prometer isso no começo, beleza (?)

então assim eu queria conversar com você
mas para você entender
né a maneira que eu penso
tal como que eu tenho esse tipo de coisa
para você não se frustrar
para saber o que é possível de fazer
e assim
eu não tô dizendo que a gente só
vai chegar nisso e acabar
não sabe mesmo se não
tem um projeto bem estruturadinho
bem curtinho
um de ser cumprido
que é possível de cumprir
e depois abre a porta pra fazer um doutorado
com uma aplicação
algo mais avançado
com outras
daqui dois anos a gente vai ter outras outra
uma outra ideia de como isso vai se dar
dá pra avançar com várias coisas

então que que a gente
próximos passos é pegar essa proposta que cê me mandou
reformular ela no mesmo molde que ela tá aí no que a gente conversou reduzindo um pouco o escopo dela né
deixando ela mais suave
assim no ponto de vista de não se comprometer tanto
beleza a gente
bate um papo desse de novo
fazendo a proposta
ela tá alinhadinha
é isso mesmo

(guess2_deleted)

pra você que tá vindo da graduação
o mestrado é algo pra você aprender a fazer pesquisa
né na verdade
onde a gente se compromete mesmo e propõe coisa
no doutorado
mestrado é o que é que você tenha
você é capaz de desenvolver uma pesquisa
onde você tem muita clareza do objetivo
e muita clareza da metodologia
feito isso
cumpriu com isso
cara isso é excelente
então cê vai ver que tanto que quando a gente for mandar
o projeto pra fapespe que
que é o mais importante (?)
que o objetivo seja muito claro, que que eu quero fazer
e que a metodologia, como eu vou fazer isso, também seja muito claro
então assim
se preocupam porque
porque é uma formação de um pesquisador
é para isso que o mestrado serve
para formar você como pesquisador
para que (?)
para que você seja capaz de chegar no doutorado
e saber fazer uma pesquisa de verdade
aprofundar
se for fazer esse tipo de proposta que tá fazendo agora
que é uma proposta que é que é mais de sei lá
eu vou fazer tal coisa
tal coisa
então aí a gente vai pro doutorado com essa
com essa ideia
por enquanto é o quê (?) 
você se capacitar como pesquisador
entender os processos
pesquisador
e uma área de humanas
que é diferente da área que vocês formam na graduação
né que é como a gente faz as coisas hein
identificando
analisando
verificando esse tipo de coisa

lembra que eu sou da computação
assim eu tenho vários orientando da computação
mas é sempre nesse passo
mas pra frente gente muda a curva
pra poder é se comprometer mais

(guess2_deleted)

que tá o título lá, pode colocar alguma coisa simples
alguma coisa assim
estudo de aplicação de llm em prontuários médicos
uma análise
um estudo
um estudo de alguma coisa assim
em vez de colocar aplicação, desenvolvimento

assim: um estudo sobre o uso de llms aplicado
a prontuários médios
alguma coisa assim
